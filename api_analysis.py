#!/usr/bin/env python3
"""
API Call Counter - Track exactly how many Gemini API calls are made
"""

import requests
import json
import time
import re
from collections import defaultdict

# Configuration
BACKEND_URL = "http://localhost:8003/api"

def count_api_calls_in_logs():
    """Count API calls from the terminal output"""
    print("🔍 Analyzing backend logs for API call patterns...")

    # This would need to be run after a processing job
    print("📋 To see API call count:")
    print("1. Run a resume processing job")
    print("2. Check backend terminal for:")
    print("   - 'LiteLLM completion()' messages")
    print("   - 'HTTP Request: POST https://generativelanguage.googleapis.com' messages")
    print("   - '429 Too Many Requests' errors")
    print("3. Count these to see the exact number of API calls")

def analyze_crewai_structure():
    """Analyze what CrewAI is doing internally"""
    print("🤖 CrewAI Framework Analysis:")
    print("=" * 50)

    print("📋 Current Architecture:")
    print("1. Resume Parser Agent + Task")
    print("   - <PERSON><PERSON><PERSON> creates internal conversation")
    print("   - Makes 3-5 API calls for thinking + execution")
    print("   - Has retry logic (3x)")
    print("   - Potential: 3-15 API calls")
    print()

    print("2. Resume Analyzer Agent + Task")
    print("   - New CrewAI conversation")
    print("   - Another 3-5 API calls")
    print("   - Same retry logic")
    print("   - Potential: 3-15 API calls")
    print()

    print("3. Resume Optimizer Agent + Task")
    print("   - Another new conversation")
    print("   - Another 3-5 API calls")
    print("   - Same retry logic")
    print("   - Potential: 3-15 API calls")
    print()

    print("4. LaTeX Generator Agent + Task")
    print("   - Final conversation")
    print("   - Another 3-5 API calls")
    print("   - Same retry logic")
    print("   - Potential: 3-15 API calls")
    print()

    print("🔢 TOTAL ESTIMATED API CALLS:")
    print("- Minimum (no retries): 12-20 calls")
    print("- Maximum (with retries): 60+ calls")
    print("- With CrewAI overhead: 50-100+ calls")

def propose_single_call_solution():
    """Show what a single-call solution would look like"""
    print("\n💡 PROPOSED SINGLE-CALL SOLUTION:")
    print("=" * 50)

    single_call_prompt = '''
    Process this resume comprehensively:

    RESUME: {resume_text}
    JOB_DESCRIPTION: {job_description}

    Return JSON with:
    {
        "parsed_resume": {
            "contact_info": {...},
            "experience": [...],
            "education": [...],
            "skills": [...]
        },
        "analysis": {
            "overall_score": 85,
            "strengths": ["..."],
            "weaknesses": ["..."],
            "recommendations": ["..."]
        },
        "optimized_resume": {
            "contact_info": {...},
            "experience": [...] // improved versions
        },
        "latex_code": "\\\\documentclass{article}..."
    }

    Focus on ATS optimization and job relevance.
    '''

    print("📝 Single Prompt Template:")
    print(single_call_prompt)
    print()
    print("✅ This would result in:")
    print("- 1 API call instead of 50-100")
    print("- 30-60 seconds instead of 5-10 minutes")
    print("- No rate limit issues")
    print("- Same or better quality results")

def main():
    """Main analysis function"""
    print("🚀 Resume AI Processing - API Call Analysis")
    print("=" * 60)

    count_api_calls_in_logs()
    print()
    analyze_crewai_structure()
    print()
    propose_single_call_solution()

    print("\n🎯 RECOMMENDATION:")
    print("Replace the entire CrewAI pipeline with a single, comprehensive API call.")
    print("This will solve the rate limiting issues and dramatically improve performance.")

if __name__ == "__main__":
    main()
