<PERSON>
Software Engineer
(555) 123-4567 | <EMAIL> | linkedin.com/in/johnsmith | github.com/johnsmith

PROFESSIONAL SUMMARY
Experienced software engineer with 5+ years of experience in full-stack development. Proficient in React, Node.js, Python, and cloud technologies. Proven track record of delivering scalable web applications and leading development teams.

EXPERIENCE

Senior Software Engineer | TechCorp Inc. | 2022 - Present
• Developed and maintained 3 enterprise web applications serving 10,000+ users
• Led a team of 4 junior developers, mentoring them on best practices
• Implemented microservices architecture reducing system latency by 40%
• Built RESTful APIs and GraphQL endpoints for mobile and web clients

Software Engineer | StartupXYZ | 2020 - 2022
• Created responsive web applications using React, TypeScript, and Node.js
• Integrated third-party APIs and payment systems (Stripe, PayPal)
• Optimized database queries resulting in 25% performance improvement
• Participated in code reviews and maintained 95% test coverage

Junior Developer | DevCorp | 2019 - 2020
• Developed frontend components using React and CSS frameworks
• Assisted in backend development using Python and Django
• Fixed bugs and performed testing across multiple environments
• Collaborated with design team to implement pixel-perfect UI components

EDUCATION
Bachelor of Science in Computer Science | University of Technology | 2019
GPA: 3.7/4.0 | Relevant Coursework: Data Structures, Algorithms, Database Systems

TECHNICAL SKILLS
Programming Languages: JavaScript, TypeScript, Python, Java, SQL
Frontend: React, Vue.js, HTML5, CSS3, Sass, Bootstrap, Tailwind CSS
Backend: Node.js, Express.js, Django, Flask, FastAPI
Databases: PostgreSQL, MongoDB, MySQL, Redis
Cloud/Tools: AWS, Docker, Kubernetes, Git, Jenkins, Jira

PROJECTS
E-commerce Platform (2023)
• Built full-stack e-commerce application with React and Node.js
• Implemented secure payment processing and inventory management
• Deployed on AWS with auto-scaling capabilities

Task Management App (2022)
• Created real-time collaborative task management tool
• Used WebSocket for live updates and notifications
• Implemented role-based access control and team collaboration features

CERTIFICATIONS
• AWS Certified Developer Associate (2023)
• MongoDB Certified Developer (2022)
