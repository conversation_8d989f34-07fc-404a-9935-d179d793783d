"""
Main FastAPI application entry point
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import health, ai_processing
# from app.api import upload  # Temporarily disabled due to missing dependencies
from app.core.config import settings

app = FastAPI(
    title=settings.API_TITLE,
    description=settings.API_DESCRIPTION,
    version=settings.API_VERSION,
    debug=settings.DEBUG
)

# Enable CORS for frontend development
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(health.router, prefix="/api", tags=["health"])
# app.include_router(upload.router, prefix="/api", tags=["upload"])  # Temporarily disabled
app.include_router(ai_processing.router, prefix="/api", tags=["ai"])

@app.get("/")
async def root():
    return {
        "message": "AI Resume Optimizer API is running",
        "version": settings.API_VERSION,
        "docs": "/docs",
        "health": "/api/health",
        "features": ["file_upload", "ai_processing", "resume_optimization"]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=settings.PORT)
