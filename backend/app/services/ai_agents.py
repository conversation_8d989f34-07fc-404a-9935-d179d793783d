"""
AI Agents Service using CrewAI and Google Gemini
Implements resume parsing, analysis, and optimization agents
"""

import os
import json
import re
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError as FutureTimeoutError
import logging
import time
import signal
import random
import threading
import platform
import queue
from datetime import datetime, timed<PERSON>ta

from crewai import Agent, Task, Crew, Process, LLM
from langchain_google_genai import ChatG<PERSON><PERSON>G<PERSON><PERSON>AI
import os

from app.core.config import settings
from app.models.file_models import ParsedResume, ResumeAnalysis, OptimizedResume, LatexResume

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# CRITICAL: Prevent CrewAI from routing to Vertex AI
# Remove ALL Google Cloud environment variables that trigger Vertex AI
for env_var in ["GOOGLE_APPLICATION_CREDENTIALS", "GOOGLE_CLOUD_PROJECT", "GCLOUD_PROJECT", "GOOGLE_CLOUD_CREDENTIALS"]:
    if env_var in os.environ:
        del os.environ[env_var]
        logger.info(f"Removed {env_var} to prevent Vertex AI routing")

# Set ONLY the Gemini API key for direct API access
os.environ["GEMINI_API_KEY"] = settings.gemini_api_key
os.environ["GOOGLE_API_KEY"] = settings.gemini_api_key  # Some versions expect this

logger.info("Environment configured for DIRECT Gemini API (NOT Vertex AI)")


@dataclass
class AgentResult:
    """Result from an AI agent execution"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None


class TokenQuotaManager:
    """Manages token quota tracking and rate limiting for Gemini 2.0 Flash"""

    def __init__(self, quota_limit: int = 800000):  # Use 80% of 1M TPM limit for safety
        self.quota_limit = quota_limit
        self.used_tokens = 0
        self.reset_time = datetime.now() + timedelta(minutes=1)
        self.lock = threading.Lock()
        self.request_count = 0
        self.max_rpm = 12  # Use 80% of 15 RPM limit for safety

    def estimate_tokens(self, text: str) -> int:
        """Rough estimate of tokens (1 token ≈ 4 characters)"""
        return len(text) // 4

    def can_proceed(self, estimated_tokens: int) -> bool:
        """Check if request can proceed without exceeding quota"""
        with self.lock:
            current_time = datetime.now()

            # Reset counters if a minute has passed
            if current_time > self.reset_time:
                self.used_tokens = 0
                self.request_count = 0
                self.reset_time = current_time + timedelta(minutes=1)

            # Check both TPM and RPM limits
            tokens_ok = (self.used_tokens + estimated_tokens) <= self.quota_limit
            requests_ok = self.request_count < self.max_rpm

            return tokens_ok and requests_ok

    def record_usage(self, tokens_used: int):
        """Record token usage and increment request count"""
        with self.lock:
            self.used_tokens += tokens_used
            self.request_count += 1

    def get_wait_time(self) -> int:
        """Get seconds to wait before next request can be made"""
        with self.lock:
            if datetime.now() < self.reset_time:
                return int((self.reset_time - datetime.now()).total_seconds())
            return 0

    def reset_quota(self):
        """Manually reset quota (for testing)"""
        with self.lock:
            self.used_tokens = 0
            self.request_count = 0
            self.reset_time = datetime.now() + timedelta(minutes=1)


class RateLimitError(Exception):
    """Custom rate limit error"""
    def __init__(self, message: str, retry_after: int = None):
        super().__init__(message)
        self.retry_after = retry_after


class GlobalRateLimiter:
    """Global rate limiter to prevent multiple concurrent API calls"""

    def __init__(self):
        self.semaphore = asyncio.Semaphore(2)  # Max 2 concurrent requests
        self.last_request_time = 0
        self.min_interval = 4.0  # Minimum 4 seconds between requests (15 RPM = 4s interval)
        self.lock = asyncio.Lock()

    async def acquire(self):
        """Acquire rate limit permission"""
        await self.semaphore.acquire()

        async with self.lock:
            now = time.time()
            time_since_last = now - self.last_request_time

            if time_since_last < self.min_interval:
                wait_time = self.min_interval - time_since_last
                logger.info(f"Rate limiting: waiting {wait_time:.1f}s before next request")
                await asyncio.sleep(wait_time)

            self.last_request_time = time.time()

    def release(self):
        """Release rate limit permission"""
        self.semaphore.release()


# Global rate limiter instance
global_rate_limiter = GlobalRateLimiter()


class CircuitBreaker:
    """Enhanced circuit breaker pattern for AI agent operations with rate limit handling"""

    def __init__(self, failure_threshold: int = 3, reset_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.rate_limit_until = None

    def can_execute(self) -> bool:
        """Check if execution is allowed"""
        current_time = time.time()

        # Check if we're in a rate limit cool-down
        if self.rate_limit_until and current_time < self.rate_limit_until:
            return False

        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if current_time - self.last_failure_time > self.reset_timeout:
                self.state = "HALF_OPEN"
                return True
            return False
        elif self.state == "HALF_OPEN":
            return True
        return False

    def on_success(self):
        """Record successful execution"""
        self.failure_count = 0
        self.state = "CLOSED"
        self.rate_limit_until = None

    def on_failure(self, is_rate_limit: bool = False, retry_after: int = None):
        """Record failed execution"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if is_rate_limit and retry_after:
            # Set rate limit cool-down period
            self.rate_limit_until = time.time() + retry_after
            logger.warning(f"Rate limit hit. Cooling down for {retry_after} seconds")
        elif self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


def timeout_wrapper(func: Callable, timeout_seconds: int, *args, **kwargs):
    """Wrapper function to add timeout to any function call (platform-safe)"""

    # Use different timeout mechanisms based on platform
    if platform.system() == "Windows":
        # Windows doesn't support signal-based timeouts reliably
        # Use threading approach instead

        result_queue = queue.Queue()
        exception_queue = queue.Queue()

        def target():
            try:
                result = func(*args, **kwargs)
                result_queue.put(result)
            except Exception as e:
                exception_queue.put(e)

        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()
        thread.join(timeout_seconds)

        if thread.is_alive():
            # Thread is still running, timeout occurred
            raise TimeoutError(f"Function timed out after {timeout_seconds} seconds")

        if not exception_queue.empty():
            raise exception_queue.get()

        if not result_queue.empty():
            return result_queue.get()
        else:
            raise TimeoutError("Function completed but no result returned")

    else:
        # Unix-like systems (Linux, macOS) - use signal-based timeout
        def timeout_handler(signum, frame):
            raise TimeoutError(f"Function timed out after {timeout_seconds} seconds")

        old_handler = signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout_seconds)

        try:
            result = func(*args, **kwargs)
            signal.alarm(0)  # Cancel the alarm
            return result
        except TimeoutError:
            raise
        except Exception as e:
            signal.alarm(0)  # Cancel the alarm
            raise e
        finally:
            signal.signal(signal.SIGALRM, old_handler)
async def timeout_wrapper_async(func: Callable, timeout_seconds: int, *args, **kwargs):
    """Async wrapper function to add timeout to any async function call"""
    try:
        return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
    except asyncio.TimeoutError:
        raise TimeoutError(f"Async function timed out after {timeout_seconds} seconds")


def extract_retry_delay(error_response: Any) -> int:
    """Extract retry delay from API error response"""
    try:
        # Always check for direct retry-after in response headers first
        if hasattr(error_response, 'response') and hasattr(error_response.response, 'headers'):
            if 'retry-after' in error_response.response.headers:
                retry_after = error_response.response.headers['retry-after']
                try:
                    return int(retry_after)
                except ValueError:
                    pass  # If it's not an integer, continue with other methods

        # Check common patterns in error messages
        error_str = str(error_response).lower()

        # Pattern: "wait X seconds"
        wait_match = re.search(r'wait\s+(\d+)\s*seconds', error_str)
        if wait_match:
            return int(wait_match.group(1))

        # Pattern: "retry after X seconds" or "retry in X seconds"
        retry_match = re.search(r'retry\s+(?:after|in)\s+(\d+)\s*seconds', error_str)
        if retry_match:
            return int(retry_match.group(1))

        # Try to parse response JSON if available
        if hasattr(error_response, 'response') and hasattr(error_response.response, 'json'):
            error_data = error_response.response.json()
        elif hasattr(error_response, 'json'):
            error_data = error_response.json()
        elif isinstance(error_response, dict):
            error_data = error_response
        else:
            # Try to parse string representation for JSON structure
            if '"retryDelay":' in str(error_response):
                match = re.search(r'"retryDelay":\s*"?(\d+)s?"?', str(error_response))
                if match:
                    return int(match.group(1))
            return 60  # More conservative default

        # Navigate the error structure to find retryDelay
        if 'error' in error_data:
            error_info = error_data['error']
            if 'details' in error_info:
                for detail in error_info['details']:
                    if detail.get('@type') == 'type.googleapis.com/google.rpc.RetryInfo':
                        retry_delay = detail.get('retryDelay', '60s')
                        # Extract number from "47s" format
                        match = re.search(r'(\d+)', retry_delay)
                        if match:
                            return int(match.group(1))
        return 60  # More conservative default fallback
    except Exception as e:
        logger.warning(f"Could not extract retry delay: {e}")
        return 60  # More conservative default fallback


async def exponential_backoff_retry(func: Callable, *args, max_retries: int = 3, base_delay: int = 2, max_delay: int = 300, **kwargs):
    """
    Execute function with exponential backoff retry logic
    Specifically handles rate limit errors with proper delay parsing
    """
    retries = 0

    # Global delay tracking - to help avoid thundering herd problem
    global_min_delay = getattr(exponential_backoff_retry, 'global_min_delay', None)
    if global_min_delay and time.time() < global_min_delay:
        # If there's a global minimum delay set, respect it
        await asyncio.sleep(global_min_delay - time.time())

    while retries < max_retries:
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            error_str = str(e).lower()
            is_rate_limit = any(x in error_str for x in ['ratelimiterror', '429', 'rate limit', 'quota'])

            if not is_rate_limit or retries >= max_retries - 1:
                if is_rate_limit:
                    # Store the extracted retry delay globally
                    retry_delay = extract_retry_delay(e)
                    if retry_delay > 0:
                        # Set global minimum delay for all future requests
                        exponential_backoff_retry.global_min_delay = time.time() + retry_delay

                raise e

            # Extract retry delay from the error
            retry_delay = extract_retry_delay(e)

            # Always use the API's suggested delay when available
            if retry_delay > 0:
                actual_delay = retry_delay
                # Also set global minimum delay for all requests
                exponential_backoff_retry.global_min_delay = time.time() + retry_delay
            else:
                # Calculate exponential backoff with increased base delay
                backoff_delay = min(base_delay * (2 ** retries), max_delay)
                actual_delay = backoff_delay

            # Add substantial jitter to prevent thundering herd
            jitter = random.uniform(1.0, 1.5)  # Increase jitter
            sleep_time = actual_delay * jitter

            logger.warning(f"Rate limit hit (attempt {retries + 1}/{max_retries}). "
                         f"Waiting {sleep_time:.1f}s (API suggested: {retry_delay}s)")

            await asyncio.sleep(sleep_time)
            retries += 1

    raise Exception(f"Max retries ({max_retries}) exceeded")


def create_direct_gemini_llm() -> LLM:
    """Create a direct Gemini LLM instance that bypasses Vertex AI routing with higher rate limits"""
    try:
        # Method 1: Use CrewAI's LLM abstraction with Gemini 2.0 Flash (better rate limits)
        llm = LLM(
            model="gemini/gemini-2.0-flash",  # Use stable 2.0-flash model (not experimental)
            temperature=0.3,  # Slightly higher for more creativity but still controlled
            max_tokens=2048,  # Reduce max tokens to save quota
            timeout=120,  # Shorter timeout
        )
        logger.info("Created CrewAI LLM with 'gemini/gemini-2.0-flash' (15 RPM, 1M TPM)")
        return llm
    except Exception as e:
        logger.warning(f"CrewAI LLM failed: {e}. Falling back to LangChain approach.")

        # Method 2: Fallback to LangChain direct Gemini API
        try:
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash",  # Use stable 2.0-flash model (not experimental)
                google_api_key=os.getenv("GEMINI_API_KEY"),
                temperature=0.3,
                max_tokens=2048,
                timeout=120,
                max_retries=2,  # Reduce retries since we have better rate limits
            )
            logger.info("Created LangChain ChatGoogleGenerativeAI with Gemini 2.0 Flash (1M TPM)")
            return llm
        except Exception as e2:
            logger.error(f"Both LLM creation methods failed: {e2}")
            raise Exception(f"Failed to create Gemini LLM: {e2}")


class ResumeAIAgents:
    """Main class for managing AI agents for resume processing with Gemini 2.0 Flash (15 RPM, 1M TPM)"""

    def __init__(self):
        """Initialize the AI agents system with Gemini 2.0 Flash and improved rate limiting"""
        logger.info("Initializing ResumeAIAgents with Gemini 2.0 Flash (15 RPM, 1M TPM)")

        # Initialize token quota manager with 2.0 Flash limits
        self.quota_manager = TokenQuotaManager()

        # Create the LLM instance using Gemini 2.0 Flash
        self.llm = create_direct_gemini_llm()

        # Initialize other components
        self.agents = self._create_agents()
        self.executor = ThreadPoolExecutor(max_workers=3)  # Increase slightly for better throughput
        self.circuit_breaker = CircuitBreaker()
        self.progress_callback: Optional[Callable] = None

        logger.info("Successfully initialized all agents with Gemini 2.0 Flash and improved rate limiting")

    def _create_agents(self) -> Dict[str, Agent]:
        """Create all AI agents with optimized, concise configurations"""

        logger.info("Creating agents with optimized prompts for rate limiting")

        # Create agents with concise prompts to reduce token usage
        parser_agent = Agent(
            role='Resume Parser',
            goal='Extract structured resume information accurately',
            backstory="""Expert at parsing resumes into structured JSON format.
            Focus on accuracy and completeness while maintaining efficiency.""",
            verbose=False,  # Reduce verbosity to save tokens
            allow_delegation=False,
            llm=self.llm,
            max_execution_time=120,  # Shorter timeout
            max_iter=2,  # Reduce iterations
        )

        analyzer_agent = Agent(
            role='Resume Analyst',
            goal='Analyze resume quality and identify improvements',
            backstory="""Professional resume evaluator who provides actionable feedback.
            Identifies strengths and weaknesses efficiently.""",
            verbose=False,
            allow_delegation=False,
            llm=self.llm,
            max_execution_time=120,
            max_iter=2,
        )

        optimizer_agent = Agent(
            role='Resume Optimizer',
            goal='Enhance resume content for maximum impact',
            backstory="""Expert resume writer who improves content with metrics,
            keywords, and compelling descriptions.""",
            verbose=False,
            allow_delegation=False,
            llm=self.llm,
            max_execution_time=120,
            max_iter=2,
        )

        latex_agent = Agent(
            role='LaTeX Formatter',
            goal='Convert resume to professional LaTeX format',
            backstory="""Technical writer specialized in creating clean,
            professional LaTeX documents.""",
            verbose=False,
            allow_delegation=False,
            llm=self.llm,
            max_execution_time=120,
            max_iter=2,
        )

        logger.info("Successfully created optimized agents")
        return {
            'parser': parser_agent,
            'analyzer': analyzer_agent,
            'optimizer': optimizer_agent,
            'latex': latex_agent
        }

    def set_progress_callback(self, callback: Callable[[str, int], None]):
        """Set a callback function for progress updates"""
        self.progress_callback = callback

    def _update_progress(self, step: str, progress: int):
        """Update progress if callback is set"""
        if self.progress_callback:
            self.progress_callback(step, progress)
        logger.info(f"Progress: {step} - {progress}%")

    def _create_parsing_task(self, resume_text: str) -> Task:
        """Create task for parsing resume text with optimized prompts"""
        return Task(
            description=f"""
            Parse this resume and extract key information in JSON format:

            {resume_text}

            Extract: contact info, work experience, education, skills, projects.
            Keep descriptions concise. Use consistent date formats (MM/YYYY).
            """,
            expected_output="""
            JSON with: contact_info, experience, education, skills, projects, summary.
            Focus on structure and accuracy over detail.
            """,
            agent=self.agents['parser']
        )

    def _create_analysis_task(self, parsed_resume: Dict[str, Any]) -> Task:
        """Create task for analyzing parsed resume with optimized prompts"""
        return Task(
            description=f"""
            Analyze this resume data and provide feedback:

            {json.dumps(parsed_resume, indent=1)}

            Rate overall strength (0-100), identify 3 key strengths and 3 improvement areas.
            Focus on content quality, completeness, and professional appeal.
            """,
            expected_output="""
            JSON with: overall_score, strengths[], weaknesses[], recommendations[].
            Keep feedback actionable and specific.
            """,
            agent=self.agents['analyzer'],
            context=[self._create_parsing_task("")]
        )

    def _create_optimization_task(self, parsed_resume: Dict[str, Any], analysis: Dict[str, Any]) -> Task:
        """Create task for optimizing resume content with optimized prompts"""
        return Task(
            description=f"""
            Optimize this resume based on analysis:

            Resume: {json.dumps(parsed_resume, indent=1)}
            Analysis: {json.dumps(analysis, indent=1)}

            Improve job descriptions, add metrics, enhance skills section.
            Use action verbs and quantify achievements where possible.
            """,
            expected_output="""
            Optimized resume JSON with improved content.
            Focus on impact and clarity.
            """,
            agent=self.agents['optimizer'],
            context=[self._create_parsing_task(""), self._create_analysis_task({})]
        )

    def _create_latex_task(self, optimized_resume: Dict[str, Any]) -> Task:
        """Create task for generating LaTeX resume with optimized prompts"""
        return Task(
            description=f"""
            Convert to professional LaTeX format:

            {json.dumps(optimized_resume, indent=1)}

            Use clean, modern layout. Ensure single-page format.
            Include all sections with proper formatting.
            """,
            expected_output="""
            Complete LaTeX document ready for compilation.
            Professional appearance, ATS-friendly structure.
            """,
            agent=self.agents['latex'],
            context=[
                self._create_parsing_task(""),
                self._create_analysis_task({}),
                self._create_optimization_task({}, {})
            ]
        )

    async def process_resume(self, resume_text: str) -> Dict[str, Any]:
        """Process resume through all AI agents with robust rate limiting and error handling"""

        if not self.circuit_breaker.can_execute():
            return {
                'success': False,
                'error': 'System temporarily unavailable due to rate limits or repeated failures.',
                'parsed_resume': None,
                'analysis': None,
                'optimized_resume': None,
                'latex_code': None
            }

        # Check token quota before proceeding (with Gemini 2.0 Flash's better limits)
        estimated_tokens = self.quota_manager.estimate_tokens(resume_text)
        if not self.quota_manager.can_proceed(estimated_tokens * 3):  # Reduce multiplier for 2.0 Flash
            wait_time = self.quota_manager.get_wait_time()
            return {
                'success': False,
                'error': f'Token quota nearly exhausted. Please wait {wait_time} seconds before submitting more requests.',
                'parsed_resume': None,
                'analysis': None,
                'optimized_resume': None,
                'latex_code': None
            }

        start_time = time.time()

        try:
            # Phase 1: Parse Resume with rate limiting
            self._update_progress("Parsing resume content", 10)
            logger.info("Starting resume parsing with rate limiting...")

            parsing_task = self._create_parsing_task(resume_text)
            parsing_crew = Crew(
                agents=[self.agents['parser']],
                tasks=[parsing_task],
                process=Process.sequential,
                verbose=False  # Reduce verbosity to save tokens
            )

            # Execute parsing with retry logic
            parsing_result = await exponential_backoff_retry(
                self._execute_crew_async,
                parsing_crew,
                max_retries=3,
                base_delay=1,
                max_delay=300
            )

            # Record token usage
            self.quota_manager.record_usage(estimated_tokens)

            # Parse the JSON result
            try:
                parsed_data = json.loads(parsing_result.raw)
                logger.info("Resume parsing completed successfully")
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\{.*\}', parsing_result.raw, re.DOTALL)
                if json_match:
                    parsed_data = json.loads(json_match.group())
                    logger.info("Resume parsing completed with JSON extraction")
                else:
                    raise ValueError("Could not extract valid JSON from parsing result")

            # Add smaller delay between API calls (Gemini 2.0 Flash has better limits)
            await asyncio.sleep(1)

            # Phase 2: Analyze Resume with rate limiting
            self._update_progress("Analyzing resume quality", 40)
            logger.info("Starting resume analysis with rate limiting...")

            analysis_task = self._create_analysis_task(parsed_data)
            analysis_crew = Crew(
                agents=[self.agents['analyzer']],
                tasks=[analysis_task],
                process=Process.sequential,
                verbose=False
            )

            analysis_result = await exponential_backoff_retry(
                self._execute_crew_async,
                analysis_crew,
                max_retries=3,
                base_delay=1,
                max_delay=300
            )

            # Parse analysis JSON
            try:
                analysis_data = json.loads(analysis_result.raw)
                logger.info("Resume analysis completed successfully")
            except json.JSONDecodeError:
                json_match = re.search(r'\{.*\}', analysis_result.raw, re.DOTALL)
                if json_match:
                    analysis_data = json.loads(json_match.group())
                    logger.info("Resume analysis completed with JSON extraction")
                else:
                    raise ValueError("Could not extract valid JSON from analysis result")

            # Add delay between API calls
            await asyncio.sleep(2)

            # Phase 3: Optimize Resume with rate limiting
            self._update_progress("Optimizing resume content", 70)
            logger.info("Starting resume optimization with rate limiting...")

            optimization_task = self._create_optimization_task(parsed_data, analysis_data)
            optimization_crew = Crew(
                agents=[self.agents['optimizer']],
                tasks=[optimization_task],
                process=Process.sequential,
                verbose=False
            )

            optimization_result = await exponential_backoff_retry(
                self._execute_crew_async,
                optimization_crew,
                max_retries=3,
                base_delay=1,
                max_delay=300
            )

            # Parse optimization JSON
            try:
                optimized_data = json.loads(optimization_result.raw)
                logger.info("Resume optimization completed successfully")
            except json.JSONDecodeError:
                json_match = re.search(r'\{.*\}', optimization_result.raw, re.DOTALL)
                if json_match:
                    optimized_data = json.loads(json_match.group())
                    logger.info("Resume optimization completed with JSON extraction")
                else:
                    raise ValueError("Could not extract valid JSON from optimization result")

            # Add delay before final phase
            await asyncio.sleep(2)

            # Phase 4: Generate LaTeX with rate limiting
            self._update_progress("Generating LaTeX format", 90)
            logger.info("Starting LaTeX generation with rate limiting...")

            latex_task = self._create_latex_task(optimized_data)
            latex_crew = Crew(
                agents=[self.agents['latex']],
                tasks=[latex_task],
                process=Process.sequential,
                verbose=False
            )

            latex_result = await exponential_backoff_retry(
                self._execute_crew_async,
                latex_crew,
                max_retries=3,
                base_delay=1,
                max_delay=300
            )

            self._update_progress("Processing completed successfully", 100)
            execution_time = time.time() - start_time
            logger.info(f"Resume processing completed in {execution_time:.2f} seconds")

            # Record success
            self.circuit_breaker.on_success()

            return {
                'success': True,
                'parsed_resume': parsed_data,
                'analysis': analysis_data,
                'optimized_resume': optimized_data,
                'latex_code': latex_result.raw,
                'execution_time': execution_time
            }

        except Exception as e:
            error_str = str(e).lower()
            is_rate_limit = any(x in error_str for x in ['ratelimiterror', '429', 'rate limit', 'quota'])

            if is_rate_limit:
                retry_after = extract_retry_delay(e)
                self.circuit_breaker.on_failure(is_rate_limit=True, retry_after=retry_after)
                error_msg = f"Rate limit exceeded. Please wait {retry_after} seconds before trying again."
            else:
                self.circuit_breaker.on_failure(is_rate_limit=False)
                error_msg = f"Processing failed: {str(e)}"

            logger.error(error_msg, exc_info=not is_rate_limit)
            return {
                'success': False,
                'error': error_msg,
                'parsed_resume': None,
                'analysis': None,
                'optimized_resume': None,
                'latex_code': None
            }

    async def _execute_crew_async(self, crew):
        """Execute crew in async context with global rate limiting"""
        # Acquire rate limit permission
        await global_rate_limiter.acquire()

        try:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, crew.kickoff)
        finally:
            # Always release the rate limiter
            global_rate_limiter.release()

    async def parse_resume_only(self, resume_text: str) -> AgentResult:
        """Parse resume text only with rate limiting"""
        # Check token quota
        estimated_tokens = self.quota_manager.estimate_tokens(resume_text)
        if not self.quota_manager.can_proceed(estimated_tokens):
            return AgentResult(
                success=False,
                error="Token quota nearly exhausted. Please wait before submitting more requests."
            )

        try:
            parsing_task = self._create_parsing_task(resume_text)
            parsing_crew = Crew(
                agents=[self.agents['parser']],
                tasks=[parsing_task],
                process=Process.sequential,
                verbose=False
            )

            result = await exponential_backoff_retry(
                self._execute_crew_async,
                parsing_crew,
                max_retries=3,
                base_delay=1,
                max_delay=300
            )

            # Record token usage
            self.quota_manager.record_usage(estimated_tokens)

            # Parse JSON result
            try:
                parsed_data = json.loads(result.raw)
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\{.*\}', result.raw, re.DOTALL)
                if json_match:
                    parsed_data = json.loads(json_match.group())
                else:
                    raise ValueError("Could not extract valid JSON")

            return AgentResult(
                success=True,
                data=parsed_data
            )

        except Exception as e:
            error_str = str(e).lower()
            is_rate_limit = any(x in error_str for x in ['ratelimiterror', '429', 'rate limit', 'quota'])

            if is_rate_limit:
                retry_after = extract_retry_delay(e)
                error_msg = f"Rate limit exceeded. Please wait {retry_after} seconds before trying again."
            else:
                error_msg = f"Parsing failed: {str(e)}"

            return AgentResult(
                success=False,
                error=error_msg
            )

    def __del__(self):
        """Cleanup thread pool"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)


# Global instance for the AI agents system
resume_ai_agents = ResumeAIAgents()


# Global instance
resume_ai_agents = ResumeAIAgents()
