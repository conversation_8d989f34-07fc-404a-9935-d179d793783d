"""
File processing service for extracting text from various file formats
"""
import os
import aiofiles
from pathlib import Path
import logging
from typing import Optional, Dict, Any
import tempfile
import asyncio
import PyPDF2
from docx import Document
import pytesseract
from PIL import Image
import io

from ..core.config import settings

logger = logging.getLogger(__name__)

class FileProcessor:
    """Service for processing uploaded files and extracting text"""

    def __init__(self):
        self.supported_formats = {
            '.pdf': self._extract_from_pdf,
            '.docx': self._extract_from_docx,
            '.doc': self._extract_from_doc,
            '.txt': self._extract_from_txt
        }

    async def extract_text(self, file_path: Path) -> str:
        """
        Extract text from a file based on its extension

        Args:
            file_path: Path to the file to process

        Returns:
            Extracted text content

        Raises:
            ValueError: If file format is not supported
            Exception: If extraction fails
        """
        try:
            file_extension = file_path.suffix.lower()

            if file_extension not in self.supported_formats:
                raise ValueError(f"Unsupported file format: {file_extension}")

            # Run extraction in thread pool to avoid blocking
            extraction_func = self.supported_formats[file_extension]
            text = await asyncio.get_event_loop().run_in_executor(
                None, extraction_func, file_path
            )

            # Clean and validate extracted text
            cleaned_text = self._clean_text(text)

            if not cleaned_text.strip():
                raise ValueError("No readable text found in file")

            logger.info(f"Successfully extracted {len(cleaned_text)} characters from {file_path.name}")
            return cleaned_text

        except Exception as e:
            logger.error(f"Text extraction failed for {file_path}: {str(e)}")
            raise

    def _extract_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF file"""
        try:
            # Dynamic import to handle missing dependencies
            try:
                import PyPDF2
            except ImportError:
                return f"PDF processing not available - missing PyPDF2 dependency. File: {file_path.name}"

            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                    except Exception as e:
                        logger.warning(f"Failed to extract text from page {page_num + 1}: {str(e)}")
                        continue

            # If no text extracted, try OCR as fallback
            if not text.strip():
                logger.info("No text found in PDF, attempting OCR...")
                text = self._extract_pdf_with_ocr(file_path)

            return text

        except Exception as e:
            logger.error(f"PDF extraction error: {str(e)}")
            raise

    def _extract_from_docx(self, file_path: Path) -> str:
        """Extract text from DOCX file"""
        try:
            # Dynamic import to handle missing dependencies
            try:
                from docx import Document
            except ImportError:
                return f"DOCX processing not available - missing python-docx dependency. File: {file_path.name}"

            doc = Document(file_path)
            text = []

            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text.append(paragraph.text)

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text.append(cell.text)

            return "\n".join(text)

        except Exception as e:
            logger.error(f"DOCX extraction error: {str(e)}")
            raise

    def _extract_from_doc(self, file_path: Path) -> str:
        """Extract text from DOC file (legacy Word format)"""
        try:
            # For DOC files, we'll try to use python-docx first
            # If that fails, we could implement antiword or other solutions
            try:
                return self._extract_from_docx(file_path)
            except Exception:
                # Fallback: return message about unsupported format
                raise ValueError(
                    "Legacy DOC format requires conversion to DOCX. "
                    "Please save your document as DOCX and try again."
                )

        except Exception as e:
            logger.error(f"DOC extraction error: {str(e)}")
            raise

    def _extract_from_txt(self, file_path: Path) -> str:
        """Extract text from plain text file"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        return file.read()
                except UnicodeDecodeError:
                    continue

            # If all encodings fail, read as binary and decode with errors
            with open(file_path, 'rb') as file:
                return file.read().decode('utf-8', errors='ignore')

        except Exception as e:
            logger.error(f"TXT extraction error: {str(e)}")
            raise

    def _extract_pdf_with_ocr(self, file_path: Path) -> str:
        """Extract text from PDF using OCR (for scanned documents)"""
        try:
            # This is a simplified OCR implementation
            # In production, you might want to use more sophisticated libraries
            # like pdf2image + pytesseract

            # For now, return empty string and log that OCR is needed
            logger.warning(f"PDF {file_path.name} appears to be scanned. OCR not implemented yet.")
            return ""

        except Exception as e:
            logger.error(f"OCR extraction error: {str(e)}")
            return ""

    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        if not text:
            return ""

        # Remove excessive whitespace
        text = " ".join(text.split())

        # Remove null characters
        text = text.replace('\x00', '')

        # Remove other control characters except newlines and tabs
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')

        # Normalize line endings
        text = text.replace('\r\n', '\n').replace('\r', '\n')

        # Remove excessive newlines
        while '\n\n\n' in text:
            text = text.replace('\n\n\n', '\n\n')

        return text.strip()

    async def get_file_info(self, file_path: Path) -> dict:
        """Get metadata information about a file"""
        try:
            stat = file_path.stat()
            return {
                "filename": file_path.name,
                "size_bytes": stat.st_size,
                "created": stat.st_ctime,
                "modified": stat.st_mtime,
                "extension": file_path.suffix.lower(),
                "exists": file_path.exists()
            }
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {str(e)}")
            return {}
