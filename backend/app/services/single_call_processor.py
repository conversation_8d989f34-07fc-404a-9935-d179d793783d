"""
Single-call AI processor for resume optimization.
Replaces the multi-agent CrewAI approach with a single comprehensive API call.
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import litellm
from ..core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SingleCallProcessor:
    """
    Processes resumes with a single comprehensive API call instead of multiple agents.
    This reduces API calls from 50-100+ to just 1, solving rate limiting issues.
    """

    def __init__(self):
        # Configure LiteLLM for Gemini
        litellm.set_verbose = False  # Reduce verbose logging

    def process_resume(self, resume_text: str, job_description: str) -> Dict[str, Any]:
        """
        Process resume with a single comprehensive API call.

        Args:
            resume_text: The original resume text
            job_description: Target job description for optimization

        Returns:
            Dictionary containing all processing results
        """
        logger.info("🚀 Starting single-call resume processing")
        start_time = datetime.now()

        try:
            # Create the comprehensive prompt
            prompt = self._create_comprehensive_prompt(resume_text, job_description)

            # Make single API call
            logger.info("📞 Making single API call to Gemini...")
            response = litellm.completion(
                model="gemini/gemini-2.0-flash-exp",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert resume writer and ATS optimization specialist. You understand both human readability and ATS requirements."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=8000,
                api_key=settings.GEMINI_API_KEY
            )

            # Parse the response
            result = self._parse_response(response.choices[0].message.content)

            # Add metadata
            processing_time = (datetime.now() - start_time).total_seconds()
            result["metadata"] = {
                "processing_time_seconds": processing_time,
                "api_calls_made": 1,
                "method": "single_call",
                "processed_at": datetime.now().isoformat()
            }

            logger.info(f"✅ Single-call processing completed in {processing_time:.2f} seconds")
            return result

        except Exception as e:
            logger.error(f"❌ Error in single-call processing: {str(e)}")
            raise e

    def _create_comprehensive_prompt(self, resume_text: str, job_description: str) -> str:
        """Create a comprehensive prompt that handles all processing in one call."""

        return f"""
You are an expert resume optimization specialist. You must process and optimize the PROVIDED resume text below, NOT create a new one.

**ORIGINAL RESUME TO OPTIMIZE:**
{resume_text}

**TARGET JOB DESCRIPTION:**
{job_description}

**CRITICAL OPTIMIZATION INSTRUCTIONS:**
1. PARSE the resume and extract ALL actual information accurately
2. ANALYZE how well it matches the job requirements (be realistic with scores)
3. OPTIMIZE the content by:
   - Rewriting the professional summary to align with the target role
   - Rephrasing experience bullets to highlight relevant achievements
   - Emphasizing skills and keywords from the job description
   - Quantifying achievements where possible
   - Improving ATS compatibility
4. SCORE realistically based on actual match quality (70-95 for good matches, 50-70 for moderate, 30-50 for poor)

**OPTIMIZATION FOCUS:**
- Transform the professional summary to emphasize experience relevant to the target role
- Reframe experience bullets to highlight achievements that matter for this job
- Add relevant keywords from the job description naturally into the content
- Maintain all factual accuracy while improving presentation and relevance

**REQUIRED TASKS:**

1. **PARSE**: Extract the actual data from the provided resume
2. **ANALYZE**: Evaluate how well it matches the target job
3. **OPTIMIZE**: Improve the content while keeping all facts accurate
4. **LATEX**: Format the optimized resume professionally

**RESPONSE FORMAT** (Return ONLY valid JSON):

```json
{{
    "parsed_resume": {{
        "contact_info": {{
            "name": "[ACTUAL name from resume]",
            "email": "[ACTUAL email from resume]",
            "phone": "[ACTUAL phone from resume]",
            "location": "[ACTUAL location from resume]",
            "linkedin": "[ACTUAL linkedin from resume if present]",
            "github": "[ACTUAL github from resume if present]"
        }},
        "professional_summary": "[ACTUAL summary from resume]",
        "experience": [
            {{
                "title": "[ACTUAL job title from resume]",
                "company": "[ACTUAL company name from resume]",
                "duration": "[ACTUAL duration from resume]",
                "description": "[ACTUAL job description from resume]",
                "achievements": ["[ACTUAL achievements from resume]"]
            }}
        ],
        "education": [
            {{
                "degree": "[ACTUAL degree from resume]",
                "institution": "[ACTUAL school from resume]",
                "graduation_date": "[ACTUAL date from resume]",
                "gpa": "[ACTUAL GPA if mentioned]",
                "relevant_coursework": ["[ACTUAL courses if mentioned]"]
            }}
        ],
        "skills": {{
            "technical": ["[ACTUAL technical skills from resume]"],
            "soft": ["[ACTUAL soft skills from resume]"],
            "tools": ["[ACTUAL tools/technologies from resume]"]
        }},
        "projects": [
            {{
                "name": "[ACTUAL project name from resume]",
                "description": "[ACTUAL project description]",
                "technologies": ["[ACTUAL technologies used]"],
                "github_url": "[ACTUAL URL if present]"
            }}
        ],
        "certifications": ["[ACTUAL certifications from resume]"]
    }},

    "analysis": {{
        "overall_score": [numeric score 0-100],
        "ats_compatibility_score": [numeric score 0-100],
        "job_match_score": [numeric score 0-100],
        "strengths": [
            "[Actual strengths based on resume content]"
        ],
        "weaknesses": [
            "[Areas for improvement based on job requirements]"
        ],
        "missing_keywords": ["[keywords from job description not in resume]"],
        "recommendations": [
            "[Specific recommendations for this resume and job]"
        ]
    }},

    "optimized_resume": {{
        "contact_info": {{
            "[Use same actual contact info but potentially enhanced formatting]"
        }},
        "professional_summary": "[Enhanced version of actual summary, optimized for target job]",
        "experience": [
            {{
                "[Enhanced versions of actual experience entries with better keywords and quantified achievements]"
            }}
        ],
        "education": [
            "[Same actual education, potentially reordered by relevance]"
        ],
        "skills": {{
            "technical": "[Actual skills + relevant ones from job description the person likely has]",
            "soft": "[Relevant soft skills that align with job requirements]",
            "tools": "[Actual tools + job-relevant ones they likely know]"
        }},
        "projects": [
            "[Actual projects enhanced and reordered for relevance to target job]"
        ],
        "certifications": "[Actual certifications highlighted]"
    }},

    "latex_code": "\documentclass[11pt,a4paper,sans]{{moderncv}}\n\moderncvstyle{{banking}}\n\moderncvcolor{{blue}}\n\n% Personal data\n\name{{[ACTUAL FIRST NAME]}}{{[ACTUAL LAST NAME]}}\n\address{{[ACTUAL ADDRESS/LOCATION]}}\n\phone{{[ACTUAL PHONE]}}\n\email{{[ACTUAL EMAIL]}}\n\social[linkedin]{{[ACTUAL LINKEDIN USERNAME]}}\n\social[github]{{[ACTUAL GITHUB USERNAME]}}\n\n\begin{{document}}\n\makecvtitle\n\n\section{{Professional Summary}}\n[Enhanced actual summary]\n\n\section{{Experience}}\n[Actual experience entries with enhanced formatting]\n\n\section{{Education}}\n[Actual education entries]\n\n\section{{Skills}}\n[Actual skills organized by category]\n\n\section{{Projects}}\n[Actual project descriptions]\n\n\end{{document}}"
}}
```
Return ONLY the JSON response with no additional text."""

    def _parse_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the AI response and extract JSON."""
        logger.info(f"📄 Raw AI response (first 500 chars): {response_text[:500]}...")

        try:
            # Find JSON content between ```json and ```
            start_marker = "```json"
            end_marker = "```"

            start_idx = response_text.find(start_marker)
            if start_idx != -1:
                start_idx += len(start_marker)
                end_idx = response_text.find(end_marker, start_idx)
                if end_idx != -1:
                    json_text = response_text[start_idx:end_idx].strip()
                    logger.info(f"🔍 Extracted JSON (first 300 chars): {json_text[:300]}...")
                    return json.loads(json_text)

            # Fallback: try to parse the entire response as JSON
            logger.info("⚠️ No JSON markers found, trying to parse entire response...")
            return json.loads(response_text.strip())

        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse AI response as JSON: {str(e)}")
            logger.error(f"📄 Full response text: {response_text}")

            # Return a basic structure if parsing fails
            return {
                "parsed_resume": {"error": "Failed to parse resume"},
                "analysis": {"overall_score": 0, "error": "Parsing failed"},
                "optimized_resume": {"error": "Optimization failed"},
                "latex_code": "% Error: Failed to generate LaTeX",
                "error": "Failed to parse AI response"
            }

    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status (for compatibility with existing API)."""
        return {
            "status": "completed",
            "progress": 100,
            "message": "Single-call processing completed",
            "api_calls_used": 1
        }
