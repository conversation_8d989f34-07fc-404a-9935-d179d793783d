"""
AI Resume Processing API endpoints
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSO<PERSON>esponse
from typing import Dict, Any, Optional
import json
import uuid
import time
from datetime import datetime, timedelta
import asyncio

from app.services.ai_agents import resume_ai_agents, AgentResult
from app.services.single_call_processor import SingleCallProcessor
from app.services.file_processor import FileProcessor
from app.models.file_models import (
    ResumeProcessingRequest,
    ResumeProcessingResponse,
    ProcessingStatus,
    ParsedResume,
    ResumeAnalysis,
    OptimizedResume,
    LatexResume
)
from app.core.config import settings

# Configuration: Use single-call processor for much better performance
USE_SINGLE_CALL_PROCESSOR = True

# Initialize processors
if USE_SINGLE_CALL_PROCESSOR:
    single_call_processor = SingleCallProcessor()
    print("🚀 Using Single-Call Processor (1 API call instead of 50-100+)")
else:
    print("⚠️ Using CrewAI Multi-Agent Processor (50-100+ API calls)")
    # resume_ai_agents already imported above

router = APIRouter(prefix="/ai", tags=["AI Processing"])

# In-memory storage for processing jobs (in production, use Redis or database)
processing_jobs: Dict[str, Dict[str, Any]] = {}


class ProcessingJobStatus:
    """Processing job status constants"""
    QUEUED = "queued"
    PARSING = "parsing"
    ANALYZING = "analyzing"
    OPTIMIZING = "optimizing"
    GENERATING_LATEX = "generating_latex"
    COMPLETED = "completed"
    FAILED = "failed"


@router.post("/process-resume", response_model=ResumeProcessingResponse)
async def process_resume(
    request: ResumeProcessingRequest,
    background_tasks: BackgroundTasks
):
    """
    Start AI processing of a resume

    This endpoint initiates the AI resume processing pipeline:
    1. Parse resume content
    2. Analyze for strengths/weaknesses
    3. Optimize content
    4. Generate LaTeX format
    """
    try:
        # Validate request
        if not request.validate_request():
            raise HTTPException(
                status_code=400,
                detail="Either file_id or resume_text must be provided"
            )

        # Generate unique job ID
        job_id = str(uuid.uuid4())

        # Initialize job status
        processing_jobs[job_id] = {
            "status": ProcessingJobStatus.QUEUED,
            "started_at": datetime.utcnow().isoformat(),
            "progress": 0,
            "current_step": "Initializing",
            "results": {},
            "error": None
        }

        # Get resume text - either from direct input or from file
        resume_text = request.resume_text
        if not resume_text and request.file_id:
            # TODO: Implement file loading from file_id
            raise HTTPException(
                status_code=400,
                detail="File loading from file_id not yet implemented. Please provide resume_text directly."
            )

        # Start background processing
        background_tasks.add_task(
            process_resume_background,
            job_id,
            resume_text,
            request.target_role,
            request.target_industry,
            request.job_description
        )

        return ResumeProcessingResponse(
            job_id=job_id,
            status=ProcessingStatus.PROCESSING,
            message="Resume processing started successfully",
            estimated_completion_time=30 if USE_SINGLE_CALL_PROCESSOR else 300  # 30 seconds vs 5 minutes
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start resume processing: {str(e)}"
        )


@router.get("/processing-status/{job_id}")
async def get_processing_status(job_id: str):
    """Get the status of a resume processing job with detailed error information"""

    # Check for rate limiting only if using CrewAI processor
    if not USE_SINGLE_CALL_PROCESSOR:
        if not resume_ai_agents.circuit_breaker.can_execute():
            rate_limit_until = resume_ai_agents.circuit_breaker.rate_limit_until
            if rate_limit_until:
                # Return retry delay information to client
                retry_seconds = max(1, int(rate_limit_until - time.time()))
                return {
                    "job_id": job_id,
                    "status": "rate_limited",
                    "progress": 0,
                    "current_step": "Waiting for rate limit to expire",
                    "error": f"Rate limit exceeded. Please wait {retry_seconds} seconds before trying again.",
                    "retry_after": retry_seconds
                }

    if job_id not in processing_jobs:
        raise HTTPException(
            status_code=404,
            detail="Processing job not found"
        )

    job = processing_jobs[job_id]

    # Calculate estimated completion time
    estimated_completion = None
    if job["status"] == ProcessingJobStatus.QUEUED or job["status"] in [
        ProcessingJobStatus.PARSING,
        ProcessingJobStatus.ANALYZING,
        ProcessingJobStatus.OPTIMIZING,
        ProcessingJobStatus.GENERATING_LATEX
    ]:
        started_at = datetime.fromisoformat(job["started_at"])
        elapsed = (datetime.utcnow() - started_at).total_seconds()
        # Estimate 5-10 minutes total processing time
        estimated_remaining = max(0, 600 - elapsed)
        estimated_completion = (datetime.utcnow() + timedelta(seconds=estimated_remaining)).isoformat()

    return {
        "job_id": job_id,
        "status": job["status"],
        "progress": job["progress"],
        "current_step": job["current_step"],
        "started_at": job["started_at"],
        "completed_at": job.get("completed_at"),
        "estimated_completion": estimated_completion,
        "error": job.get("error"),
        "error_details": {
            "has_error": bool(job.get("error")),
            "error_type": "timeout" if "timed out" in str(job.get("error", "")).lower() else
                         "json_parse" if "JSON" in str(job.get("error", "")) else
                         "llm_failure" if "LLM" in str(job.get("error", "")) else
                         "unknown" if job.get("error") else None,
            "is_retryable": job["status"] != ProcessingJobStatus.FAILED or
                           "timed out" in str(job.get("error", "")).lower()
        },
        "has_results": bool(job["results"]),
        "processing_time": job.get("processing_time")
    }


@router.get("/results/{job_id}")
async def get_processing_results(job_id: str):
    """Get the results of a completed resume processing job"""

    if job_id not in processing_jobs:
        raise HTTPException(
            status_code=404,
            detail="Processing job not found"
        )

    job = processing_jobs[job_id]

    if job["status"] != ProcessingJobStatus.COMPLETED:
        raise HTTPException(
            status_code=400,
            detail=f"Job is not completed. Current status: {job['status']}"
        )

    return {
        "job_id": job_id,
        "status": job["status"],
        "results": job["results"],
        "processing_time": job.get("processing_time"),
        "completed_at": job.get("completed_at")
    }


@router.post("/parse-only")
async def parse_resume_only(request: ResumeProcessingRequest):
    """
    Parse resume content only (quick parsing without full analysis)
    """
    try:
        result = await resume_ai_agents.parse_resume_only(request.resume_text)

        if not result.success:
            raise HTTPException(
                status_code=500,
                detail=f"Resume parsing failed: {result.error}"
            )

        return {
            "success": True,
            "parsed_resume": result.data,
            "processing_time": result.execution_time
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Resume parsing failed: {str(e)}"
        )


@router.get("/agents/status")
async def get_agents_status():
    """Get the status of AI agents"""
    try:
        return {
            "agents_available": True,
            "gemini_configured": bool(settings.gemini_api_key),
            "agents": {
                "parser": "Available",
                "analyzer": "Available",
                "optimizer": "Available",
                "latex_generator": "Available"
            },
            "last_health_check": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "agents_available": False,
            "error": str(e),
            "last_health_check": datetime.utcnow().isoformat()
        }


@router.post("/retry-job/{job_id}")
async def retry_processing_job(
    job_id: str,
    background_tasks: BackgroundTasks
):
    """Retry a failed processing job"""

    if job_id not in processing_jobs:
        raise HTTPException(
            status_code=404,
            detail="Processing job not found"
        )

    job = processing_jobs[job_id]

    if job["status"] != ProcessingJobStatus.FAILED:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot retry job with status: {job['status']}"
        )

    # Check if error is retryable
    error_msg = job.get("error", "")
    if "circuit breaker" in error_msg.lower():
        raise HTTPException(
            status_code=503,
            detail="System is temporarily unavailable. Please try again later."
        )

    # Reset job status
    job["status"] = ProcessingJobStatus.QUEUED
    job["progress"] = 0
    job["current_step"] = "Retrying processing"
    job["error"] = None
    job["started_at"] = datetime.utcnow().isoformat()

    # Extract original parameters (this is a simplified approach)
    # In a real system, you'd store the original request parameters
    resume_text = "Resume content to be processed"  # This should come from stored job data

    # Start background processing again
    background_tasks.add_task(
        process_resume_background,
        job_id,
        resume_text,
        None,  # target_role
        None   # target_industry
    )

    return {
        "message": "Job retry initiated",
        "job_id": job_id,
        "status": ProcessingJobStatus.QUEUED
    }


@router.get("/system/health")
async def get_system_health():
    """Get detailed system health information"""
    try:
        # Test AI agents availability
        agents_healthy = True
        llm_error = None
        try:
            # Quick test of the AI agents
            if hasattr(resume_ai_agents, 'llm') and resume_ai_agents.llm:
                agents_healthy = True
                llm_error = None
            else:
                agents_healthy = False
                llm_error = "AI agents not properly initialized"
        except Exception as e:
            agents_healthy = False
            llm_error = str(e)

        # Count job statuses
        job_stats = {
            "total": len(processing_jobs),
            "queued": sum(1 for job in processing_jobs.values() if job["status"] == ProcessingJobStatus.QUEUED),
            "processing": sum(1 for job in processing_jobs.values() if job["status"] in [
                ProcessingJobStatus.PARSING, ProcessingJobStatus.ANALYZING,
                ProcessingJobStatus.OPTIMIZING, ProcessingJobStatus.GENERATING_LATEX
            ]),
            "completed": sum(1 for job in processing_jobs.values() if job["status"] == ProcessingJobStatus.COMPLETED),
            "failed": sum(1 for job in processing_jobs.values() if job["status"] == ProcessingJobStatus.FAILED)
        }

        # Circuit breaker status
        circuit_breaker_status = {
            "state": resume_ai_agents.circuit_breaker.state,
            "failure_count": resume_ai_agents.circuit_breaker.failure_count,
            "can_execute": resume_ai_agents.circuit_breaker.can_execute()
        }

        return {
            "status": "healthy" if agents_healthy and resume_ai_agents.circuit_breaker.can_execute() else "degraded",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "ai_agents": {
                    "status": "healthy" if agents_healthy else "unhealthy",
                    "error": llm_error
                },
                "gemini_llm": {
                    "configured": bool(settings.gemini_api_key),
                    "status": "healthy" if agents_healthy else "unhealthy"
                },
                "circuit_breaker": circuit_breaker_status,
                "job_processing": {
                    "status": "healthy",
                    "stats": job_stats
                }
            },
            "recommendations": [
                "Check GEMINI_API_KEY environment variable" if not settings.gemini_api_key else None,
                "Wait for circuit breaker to reset" if not resume_ai_agents.circuit_breaker.can_execute() else None,
                f"System has {job_stats['failed']} failed jobs" if job_stats["failed"] > 0 else None
            ]
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }


@router.delete("/jobs/{job_id}")
async def cancel_processing_job(job_id: str):
    """Cancel a processing job"""

    if job_id not in processing_jobs:
        raise HTTPException(
            status_code=404,
            detail="Processing job not found"
        )

    job = processing_jobs[job_id]

    if job["status"] in [ProcessingJobStatus.COMPLETED, ProcessingJobStatus.FAILED]:
        raise HTTPException(
            status_code=400,
            detail="Cannot cancel completed or failed job"
        )

    # Mark job as cancelled
    job["status"] = "cancelled"
    job["completed_at"] = datetime.utcnow().isoformat()

    return {"message": "Job cancelled successfully"}


@router.get("/jobs")
async def list_processing_jobs(limit: int = 50):
    """List recent processing jobs"""

    # Sort jobs by start time (most recent first)
    sorted_jobs = sorted(
        processing_jobs.items(),
        key=lambda x: x[1]["started_at"],
        reverse=True
    )

    # Return limited results
    jobs = []
    for job_id, job_data in sorted_jobs[:limit]:
        jobs.append({
            "job_id": job_id,
            "status": job_data["status"],
            "started_at": job_data["started_at"],
            "completed_at": job_data.get("completed_at"),
            "progress": job_data["progress"],
            "current_step": job_data["current_step"]
        })

    return {
        "jobs": jobs,
        "total_jobs": len(processing_jobs)
    }


async def process_resume_background(
    job_id: str,
    resume_text: str,
    target_role: Optional[str] = None,
    target_industry: Optional[str] = None,
    job_description: Optional[str] = None
):
    """Background task for processing resume with single-call processor (much faster!)"""

    job = processing_jobs[job_id]
    start_time = datetime.utcnow()

    def update_progress(step: str, progress: int):
        """Update job progress and status"""
        job["current_step"] = step
        job["progress"] = progress
        print(f"Job {job_id}: {step} - {progress}%")

    try:
        # Initialize processing
        job["status"] = ProcessingJobStatus.QUEUED
        update_progress("Initializing single-call processor", 10)

        # Prepare job description context
        final_job_description = job_description or ""
        if not final_job_description and (target_role or target_industry):
            final_job_description = f"Target Role: {target_role or 'Not specified'}\nTarget Industry: {target_industry or 'Not specified'}"

        # Check which processor to use
        if USE_SINGLE_CALL_PROCESSOR:
            # Use the efficient single-call processor
            update_progress("Processing with single API call", 20)
            job["status"] = ProcessingJobStatus.PARSING

            results = single_call_processor.process_resume(
                resume_text=resume_text,
                job_description=final_job_description
            )

            # Single call completes all steps instantly
            update_progress("Single-call processing completed", 100)

        else:
            # Use the original CrewAI approach (much slower, many API calls)
            update_progress("Starting multi-agent processing", 20)

            # Add context if provided
            enhanced_text = resume_text
            if target_role or target_industry:
                context = f"\nTarget Role: {target_role or 'Not specified'}\nTarget Industry: {target_industry or 'Not specified'}"
                enhanced_text = f"{resume_text}\n\nCONTEXT:{context}"

            # Set up progress callback for AI agents
            resume_ai_agents.set_progress_callback(update_progress)

            # Update status to processing
            job["status"] = ProcessingJobStatus.PARSING
            update_progress("Starting resume processing", 30)

            # Process through AI agents with timeout
            try:
                results = await asyncio.wait_for(
                    resume_ai_agents.process_resume(enhanced_text),
                    timeout=1200  # 20 minutes total timeout
                )
            except asyncio.TimeoutError:
                raise TimeoutError("Resume processing timed out after 20 minutes")

            if not results["success"]:
                raise Exception(f"AI processing failed: {results['error']}")

        # Complete processing
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()

        job["status"] = ProcessingJobStatus.COMPLETED
        job["progress"] = 100
        job["current_step"] = f"Processing completed in {processing_time:.1f}s"
        job["completed_at"] = end_time.isoformat()
        job["processing_time"] = processing_time

        # Format results consistently
        if USE_SINGLE_CALL_PROCESSOR:
            job["results"] = {
                "parsed_resume": results.get("parsed_resume", {}),
                "analysis": results.get("analysis", {}),
                "optimized_resume": results.get("optimized_resume", {}),
                "latex_code": results.get("latex_code", ""),
                "metadata": results.get("metadata", {})
            }
        else:
            job["results"] = {
                "parsed_resume": results["parsed_resume"],
                "analysis": results["analysis"],
                "optimized_resume": results["optimized_resume"],
                "latex_code": results["latex_code"],
                "metadata": {
                    "processing_time": processing_time,
                    "target_role": target_role,
                    "target_industry": target_industry,
                    "processed_at": end_time.isoformat(),
                }
            }

        print(f"✅ Job {job_id} completed successfully in {processing_time:.2f} seconds")

    except Exception as e:
        # Handle errors
        job["status"] = ProcessingJobStatus.FAILED
        job["progress"] = 0
        job["current_step"] = "Processing failed"
        job["error"] = str(e)
        job["failed_at"] = datetime.utcnow().isoformat()

        print(f"❌ Job {job_id} failed: {str(e)}")

        # Log the full error for debugging
        import traceback
        traceback.print_exc()


# Cleanup old jobs periodically (in production, use proper job queue)
@router.on_event("startup")
async def startup_cleanup():
    """Clean up old jobs on startup"""
    # This would be handled by a proper job queue in production
    pass
