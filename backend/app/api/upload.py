"""
File upload endpoints for resume processing
"""
from fastapi import APIRouter, UploadFile, File, HTTPException, status
from fastapi.responses import JSONResponse
from typing import List, Optional
import os
try:
    import magic
except ImportError:
    magic = None
import asyncio
from pathlib import Path
import uuid
from datetime import datetime
import logging

from ..models.file_models import FileUploadResponse, FileValidationError
from ..services.file_processor import FileProcessor
from ..core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

# Allowed file types and their MIME types
ALLOWED_MIME_TYPES = {
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'application/msword': ['.doc'],
    'text/plain': ['.txt']
}

MAX_FILE_SIZE = settings.MAX_FILE_SIZE  # Already in bytes

def validate_file_type(file_content: bytes, filename: str) -> bool:
    """Validate file type using both extension and content detection"""
    try:
        # Get file extension
        file_extension = Path(filename).suffix.lower()

        # Detect MIME type from content
        if magic:
            mime_type = magic.from_buffer(file_content, mime=True)
        else:
            # Fallback to extension-based detection
            if file_extension == '.pdf':
                mime_type = 'application/pdf'
            elif file_extension in ['.doc', '.docx']:
                mime_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            elif file_extension == '.txt':
                mime_type = 'text/plain'
            else:
                return False

        # Check if MIME type is allowed and extension matches
        if mime_type in ALLOWED_MIME_TYPES:
            allowed_extensions = ALLOWED_MIME_TYPES[mime_type]
            return file_extension in allowed_extensions

        return False
    except Exception as e:
        logger.error(f"File type validation error: {str(e)}")
        return False

def validate_file_size(file_size: int) -> bool:
    """Validate file size"""
    return file_size <= MAX_FILE_SIZE

async def save_uploaded_file(file: UploadFile, file_id: str) -> Path:
    """Save uploaded file to storage directory"""
    try:
        # Create upload directory if it doesn't exist
        upload_dir = Path(settings.UPLOAD_DIR)
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Generate unique filename
        file_extension = Path(file.filename).suffix.lower()
        safe_filename = f"{file_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_extension}"
        file_path = upload_dir / safe_filename

        # Save file
        content = await file.read()
        with open(file_path, 'wb') as f:
            f.write(content)

        return file_path
    except Exception as e:
        logger.error(f"File save error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save uploaded file"
        )

@router.post("/upload", response_model=FileUploadResponse)
async def upload_resume(
    file: UploadFile = File(..., description="Resume file (PDF, DOC, DOCX, or TXT)")
) -> FileUploadResponse:
    """
    Upload a resume file for processing

    Accepts PDF, DOC, DOCX, and TXT files up to 10MB
    """
    try:
        # Check if file was uploaded
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file uploaded"
            )

        # Read file content for validation
        content = await file.read()
        await file.seek(0)  # Reset file pointer

        # Validate file size
        if not validate_file_size(len(content)):
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE_MB}MB"
            )

        # Validate file type
        if not validate_file_type(content, file.filename):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file type. Only PDF, DOC, DOCX, and TXT files are allowed"
            )

        # Generate unique file ID
        file_id = str(uuid.uuid4())

        # Save file
        file_path = await save_uploaded_file(file, file_id)

        # Initialize file processor
        processor = FileProcessor()

        # Extract text from file
        extracted_text = await processor.extract_text(file_path)

        if not extracted_text.strip():
            # Clean up file if no text extracted
            file_path.unlink(missing_ok=True)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No readable text found in the uploaded file"
            )

        # Log successful upload
        logger.info(f"File uploaded successfully: {file.filename} -> {file_id}")

        return FileUploadResponse(
            file_id=file_id,
            filename=file.filename,
            file_size=len(content),
            mime_type=magic.from_buffer(content, mime=True) if magic else "application/octet-stream",
            upload_timestamp=datetime.now(),
            text_extracted=True,
            character_count=len(extracted_text),
            word_count=len(extracted_text.split()),
            status="uploaded"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing the upload"
        )

@router.post("/upload/multiple", response_model=List[FileUploadResponse])
async def upload_multiple_resumes(
    files: List[UploadFile] = File(..., description="Multiple resume files")
) -> List[FileUploadResponse]:
    """
    Upload multiple resume files for batch processing
    """
    if len(files) > 5:  # Limit batch uploads
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Maximum 5 files allowed per batch upload"
        )

    results = []
    errors = []

    for file in files:
        try:
            # Process each file individually
            result = await upload_resume(file)
            results.append(result)
        except HTTPException as e:
            errors.append({
                "filename": file.filename,
                "error": e.detail
            })

    if errors and not results:
        # All files failed
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": "All files failed to upload", "errors": errors}
        )
    elif errors:
        # Partial success
        return JSONResponse(
            status_code=status.HTTP_207_MULTI_STATUS,
            content={
                "successful_uploads": [result.dict() for result in results],
                "failed_uploads": errors
            }
        )

    return results

@router.get("/upload/{file_id}/status")
async def get_upload_status(file_id: str):
    """Get the status of an uploaded file"""
    try:
        # This would typically check a database or file system
        upload_dir = Path(settings.UPLOAD_DIR)

        # Find file with matching ID
        matching_files = list(upload_dir.glob(f"{file_id}_*"))

        if not matching_files:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        file_path = matching_files[0]
        file_stats = file_path.stat()

        return {
            "file_id": file_id,
            "status": "processed",
            "file_size": file_stats.st_size,
            "upload_time": datetime.fromtimestamp(file_stats.st_ctime),
            "exists": True
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Status check error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error checking file status"
        )

@router.delete("/upload/{file_id}")
async def delete_uploaded_file(file_id: str):
    """Delete an uploaded file"""
    try:
        upload_dir = Path(settings.UPLOAD_DIR)

        # Find and delete file with matching ID
        matching_files = list(upload_dir.glob(f"{file_id}_*"))

        if not matching_files:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        for file_path in matching_files:
            file_path.unlink()

        logger.info(f"File deleted: {file_id}")

        return {"message": "File deleted successfully", "file_id": file_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting file"
        )
