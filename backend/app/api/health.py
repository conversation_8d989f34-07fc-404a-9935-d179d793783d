"""
Health check endpoints
"""
from fastapi import APIRouter
import sys
import platform

router = APIRouter()

@router.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "message": "AI Resume Optimizer API is running",
        "python_version": sys.version,
        "platform": platform.system()
    }

@router.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with dependencies"""
    try:
        import crewai
        import google.generativeai as genai
        import PyPDF2

        return {
            "status": "healthy",
            "dependencies": {
                "crewai": "installed",
                "google-generativeai": "installed",
                "pypdf2": "installed"
            },
            "api_version": "0.1.0"
        }
    except ImportError as e:
        return {
            "status": "degraded",
            "error": f"Missing dependency: {str(e)}",
            "api_version": "0.1.0"
        }
