"""
Application configuration and settings
"""
import os
from pathlib import Path
from typing import Optional, List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""

    # Environment Configuration
    environment: str = "development"
    debug: bool = False

    # API Configuration
    api_title: str = "Resume AI Optimizer"
    api_version: str = "1.0.0"
    api_description: str = "AI-powered resume analysis and optimization service"
    port: int = 8003

    # CORS Configuration
    allowed_origins: str = "http://localhost:3000,http://localhost:3001"

    # File Upload Configuration
    max_file_size_mb: int = 10
    upload_dir: str = "uploads"

    # AI Configuration
    google_api_key: str = ""
    gemini_api_key: str = ""

    # Legacy uppercase properties for compatibility
    @property
    def API_TITLE(self) -> str:
        return self.api_title

    @property
    def API_VERSION(self) -> str:
        return self.api_version

    @property
    def API_DESCRIPTION(self) -> str:
        return self.api_description

    @property
    def DEBUG(self) -> bool:
        return self.debug

    @property
    def PORT(self) -> int:
        return self.port

    @property
    def ALLOWED_ORIGINS(self) -> List[str]:
        return [origin.strip() for origin in self.allowed_origins.split(",")]

    @property
    def MAX_FILE_SIZE(self) -> int:
        return self.max_file_size_mb * 1024 * 1024

    @property
    def UPLOAD_DIR(self) -> str:
        return self.upload_dir

    @property
    def GEMINI_API_KEY(self) -> str:
        return self.google_api_key or self.gemini_api_key

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore"  # This allows extra fields in .env to be ignored
    }


settings = Settings()
