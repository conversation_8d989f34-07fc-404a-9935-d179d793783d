#!/usr/bin/env python3
"""
Debug script to see the actual resume processing results.
This will show what data the AI is extracting and optimizing.
"""

import requests
import json
import time

def test_and_show_results():
    """Test the processor and show detailed results."""

    print("🔍 Resume Processing Results Viewer")
    print("=" * 50)

    # Read test resume
    with open('sample-docs/test-resume.txt', 'r') as f:
        resume_text = f.read()

    print("📄 ORIGINAL RESUME:")
    print("-" * 30)
    print(resume_text[:500] + "..." if len(resume_text) > 500 else resume_text)
    print()

    # Test payload
    payload = {
        "resume_text": resume_text,
        "target_role": "Software Engineer - AI/ML",
        "target_industry": "Technology"
    }

    # Start processing
    print("🔄 Starting processing...")
    response = requests.post(
        "http://localhost:8003/api/ai/process-resume",
        json=payload,
        timeout=60
    )

    if response.status_code != 200:
        print(f"❌ Failed: {response.status_code}")
        print(response.text)
        return

    job_id = response.json()["job_id"]
    print(f"📋 Job ID: {job_id}")

    # Wait for completion
    while True:
        status_response = requests.get(f"http://localhost:8003/api/ai/processing-status/{job_id}")
        status = status_response.json()

        if status["status"] == "completed":
            break
        elif status["status"] == "failed":
            print(f"❌ Failed: {status.get('error', 'Unknown')}")
            return

        time.sleep(2)

    # Get results
    results_response = requests.get(f"http://localhost:8003/api/ai/results/{job_id}")

    if results_response.status_code != 200:
        print(f"❌ Failed to get results: {results_response.status_code}")
        return

    results = results_response.json()["results"]

    print("✅ Processing completed! Here are the results:\n")

    # Show parsed resume
    print("📋 PARSED RESUME (what AI extracted):")
    print("-" * 40)
    parsed = results.get("parsed_resume", {})

    contact = parsed.get("contact_info", {})
    print(f"Name: {contact.get('name', 'N/A')}")
    print(f"Email: {contact.get('email', 'N/A')}")
    print(f"Phone: {contact.get('phone', 'N/A')}")
    print(f"Location: {contact.get('location', 'N/A')}")
    print()

    print("Experience:")
    for exp in parsed.get("experience", []):
        print(f"  • {exp.get('title', 'N/A')} at {exp.get('company', 'N/A')}")
        print(f"    Duration: {exp.get('duration', 'N/A')}")
    print()

    # Show analysis
    print("📊 ANALYSIS:")
    print("-" * 20)
    analysis = results.get("analysis", {})
    print(f"Overall Score: {analysis.get('overall_score', 'N/A')}")
    print(f"ATS Score: {analysis.get('ats_compatibility_score', 'N/A')}")
    print(f"Job Match: {analysis.get('job_match_score', 'N/A')}")
    print()

    print("Strengths:")
    for strength in analysis.get("strengths", []):
        print(f"  ✅ {strength}")
    print()

    print("Weaknesses:")
    for weakness in analysis.get("weaknesses", []):
        print(f"  ⚠️ {weakness}")
    print()

    # Show optimized version
    print("🚀 OPTIMIZED RESUME (improvements):")
    print("-" * 35)
    optimized = results.get("optimized_resume", {})

    opt_contact = optimized.get("contact_info", {})
    print(f"Name: {opt_contact.get('name', 'N/A')}")
    print(f"Email: {opt_contact.get('email', 'N/A')}")
    print()

    print("Enhanced Summary:")
    print(f"  {optimized.get('professional_summary', 'N/A')}")
    print()

    print("Enhanced Experience:")
    for exp in optimized.get("experience", []):
        print(f"  • {exp.get('title', 'N/A')} at {exp.get('company', 'N/A')}")
    print()

    # Check if it's using actual data
    original_lower = resume_text.lower()
    parsed_name = contact.get('name', '').lower()

    print("🔍 DATA VERIFICATION:")
    print("-" * 25)

    if parsed_name and parsed_name != 'john doe' and parsed_name != 'full name':
        print(f"✅ Using actual name: {contact.get('name', 'N/A')}")
    else:
        print(f"❌ Using generic name: {contact.get('name', 'N/A')}")

    if contact.get('email', '').endswith('@email.com') or contact.get('email', '') == '<EMAIL>':
        print(f"❌ Using generic email: {contact.get('email', 'N/A')}")
    else:
        print(f"✅ Using actual email: {contact.get('email', 'N/A')}")

    # Check if company names are real
    exp_companies = [exp.get('company', '') for exp in parsed.get("experience", [])]
    if any('Tech Company' in comp or 'Company Name' in comp for comp in exp_companies):
        print("❌ Using generic company names")
    else:
        print(f"✅ Using actual companies: {', '.join(exp_companies[:2])}")

if __name__ == "__main__":
    test_and_show_results()
