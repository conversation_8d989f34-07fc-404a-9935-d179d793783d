"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Upload, FileText, Sparkles, Download, ArrowRight, CheckCircle } from "lucide-react";
import FileUploadSection from "@/components/sections/FileUploadSection";
import JobDescriptionSection from "@/components/sections/JobDescriptionSection";
import OptimizationSection from "@/components/sections/OptimizationSection";
import ResultsSection from "@/components/sections/ResultsSection";

type Step = "upload" | "job-description" | "optimization" | "results";

interface ProcessingResultsResponse {
  job_id: string;
  status: string;
  results: {
    parsed_resume: Record<string, unknown>;
    analysis: Record<string, unknown>;
    optimized_resume: Record<string, unknown>;
    latex_code: string;
    metadata: Record<string, unknown>;
  };
  processing_time?: number;
  completed_at?: string;
}

const steps = [
  {
    id: "upload" as Step,
    title: "Upload Resume",
    description: "Upload your existing resume",
    icon: Upload,
    timeEstimate: "2 min"
  },
  {
    id: "job-description" as Step,
    title: "Job Description",
    description: "Paste the job description",
    icon: FileText,
    timeEstimate: "1 min"
  },
  {
    id: "optimization" as Step,
    title: "AI Optimization",
    description: "AI agents optimize your resume",
    icon: Sparkles,
    timeEstimate: "5-8 min"
  },
  {
    id: "results" as Step,
    title: "Review & Download",
    description: "Review and download your optimized resume",
    icon: Download,
    timeEstimate: "2 min"
  }
];

export default function Home() {
  const [currentStep, setCurrentStep] = useState<Step>("upload");
  const [completedSteps, setCompletedSteps] = useState<Set<Step>>(new Set());
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [jobDescription, setJobDescription] = useState("");
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [processingResults, setProcessingResults] = useState<ProcessingResultsResponse | null>(null);

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const progressPercentage = ((currentStepIndex + 1) / steps.length) * 100;

  const handleStepComplete = (step: Step) => {
    setCompletedSteps(prev => new Set([...prev, step]));

    // Move to next step
    const currentIndex = steps.findIndex(s => s.id === step);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1].id);
    }
  };

  const handleStepChange = (step: Step) => {
    // Allow navigation to completed steps or the current/next step
    const stepIndex = steps.findIndex(s => s.id === step);
    const currentIndex = steps.findIndex(s => s.id === currentStep);

    if (completedSteps.has(step) || stepIndex <= currentIndex + 1) {
      setCurrentStep(step);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="h-8 w-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">ResumeAI Pro</h1>
                <p className="text-sm text-gray-500">AI-Powered Resume Optimizer</p>
              </div>
            </div>
            <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
              Beta
            </Badge>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-900">
              Transform Your Resume in Minutes
            </h2>
            <div className="text-sm text-gray-500">
              Step {currentStepIndex + 1} of {steps.length}
            </div>
          </div>

          <Progress value={progressPercentage} className="h-2 mb-6" />

          {/* Step Navigation */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {steps.map((step, index) => {
              const isCompleted = completedSteps.has(step.id);
              const isCurrent = currentStep === step.id;
              const isAccessible = isCompleted || isCurrent ||
                (index === 0) ||
                completedSteps.has(steps[index - 1].id);

              return (
                <Card
                  key={step.id}
                  className={`cursor-pointer transition-all duration-200 ${
                    isCurrent
                      ? "ring-2 ring-blue-500 bg-blue-50 border-blue-200"
                      : isCompleted
                      ? "bg-green-50 border-green-200"
                      : isAccessible
                      ? "hover:bg-gray-50 border-gray-200"
                      : "opacity-50 cursor-not-allowed border-gray-100"
                  }`}
                  onClick={() => isAccessible && handleStepChange(step.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${
                        isCurrent
                          ? "bg-blue-600 text-white"
                          : isCompleted
                          ? "bg-green-600 text-white"
                          : "bg-gray-100 text-gray-600"
                      }`}>
                        {isCompleted ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <step.icon className="h-5 w-5" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-sm text-gray-900 truncate">
                          {step.title}
                        </h3>
                        <p className="text-xs text-gray-500 truncate">
                          {step.description}
                        </p>
                        <p className="text-xs text-blue-600 mt-1">
                          {step.timeEstimate}
                        </p>
                      </div>
                      {(isCurrent && !isCompleted) && (
                        <ArrowRight className="h-4 w-4 text-blue-600" />
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Step Content */}
        <div className="space-y-8">
          {currentStep === "upload" && (
            <FileUploadSection
              uploadedFile={uploadedFile}
              onFileUpload={setUploadedFile}
              onComplete={() => handleStepComplete("upload")}
            />
          )}

          {currentStep === "job-description" && (
            <JobDescriptionSection
              jobDescription={jobDescription}
              onJobDescriptionChange={setJobDescription}
              onComplete={() => handleStepComplete("job-description")}
            />
          )}

          {currentStep === "optimization" && (
            <OptimizationSection
              uploadedFile={uploadedFile}
              jobDescription={jobDescription}
              progress={optimizationProgress}
              onProgressUpdate={setOptimizationProgress}
              onComplete={(results) => {
                if (results) {
                  setProcessingResults(results);
                }
                handleStepComplete("optimization");
              }}
            />
          )}

          {currentStep === "results" && (
            <ResultsSection
              uploadedFile={uploadedFile}
              jobDescription={jobDescription}
              processingResults={processingResults}
            />
          )}
        </div>

        {/* Footer Info */}
        <div className="mt-16 text-center">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-2">90%+</div>
                  <p className="text-sm text-gray-600">User Satisfaction Rate</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-2">15 min</div>
                  <p className="text-sm text-gray-600">Average Completion Time</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-2">40%+</div>
                  <p className="text-sm text-gray-600">Improved ATS Match Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}

