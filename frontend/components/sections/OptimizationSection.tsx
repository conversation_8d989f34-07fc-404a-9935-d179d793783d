"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Search,
  Zap,
  FileType,
  CheckCircle,
  Bot,
  Clock,
  TrendingUp,
  AlertCircle
} from "lucide-react";
import { resumeAPI } from "@/lib/api";

interface OptimizationSectionProps {
  uploadedFile: File | null;
  jobDescription: string;
  progress: number;
  onProgressUpdate: (progress: number) => void;
  onComplete: (results?: ProcessingResultsResponse) => void;
}

interface ProcessingResultsResponse {
  job_id: string;
  status: string;
  results: {
    parsed_resume: Record<string, unknown>;
    analysis: Record<string, unknown>;
    optimized_resume: Record<string, unknown>;
    latex_code: string;
    metadata: Record<string, unknown>;
  };
  processing_time?: number;
  completed_at?: string;
}

const optimizationSteps = [
  {
    id: 'parsing',
    title: 'Resume Parser Agent',
    description: 'Extracting and analyzing your resume content',
    icon: FileText,
    duration: 8000
  },
  {
    id: 'analysis',
    title: 'Job Analysis Agent',
    description: 'Analyzing job requirements and keywords',
    icon: Search,
    duration: 6000
  },
  {
    id: 'optimization',
    title: 'Content Optimizer Agent',
    description: 'Optimizing content for maximum impact',
    icon: Zap,
    duration: 12000
  },
  {
    id: 'latex',
    title: 'LaTeX Generator Agent',
    description: 'Generating professional LaTeX formatting',
    icon: FileType,
    duration: 10000
  },
  {
    id: 'finalization',
    title: 'Quality Assurance',
    description: 'Final review and PDF compilation',
    icon: CheckCircle,
    duration: 4000
  }
];

export default function OptimizationSection({
  uploadedFile,
  jobDescription,
  progress,
  onProgressUpdate,
  onComplete
}: OptimizationSectionProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [stepProgress, setStepProgress] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [insights, setInsights] = useState<string[]>([]);
  const [jobId, setJobId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStepName, setCurrentStepName] = useState<string>("");

  const addInsightForStep = useCallback((stepName: string) => {
    const insightMap: Record<string, string[]> = {
      parsing: [
        "Extracting contact information and personal details",
        "Identifying work experience and job titles",
        "Parsing education and certification details",
        "Analyzing skills and technical competencies"
      ],
      analyzing: [
        "Evaluating resume against job requirements",
        "Identifying keyword alignment opportunities",
        "Assessing content structure and format",
        "Analyzing achievement quantification"
      ],
      optimizing: [
        "Enhancing bullet points with action verbs",
        "Improving keyword density for ATS optimization",
        "Restructuring content for maximum impact",
        "Quantifying achievements and results"
      ],
      generating: [
        "Applying professional LaTeX template",
        "Optimizing layout and typography",
        "Ensuring ATS-friendly formatting",
        "Generating final PDF output"
      ]
    };

    const stepKey = stepName.toLowerCase().includes('parsing') ? 'parsing' :
                   stepName.toLowerCase().includes('analy') ? 'analyzing' :
                   stepName.toLowerCase().includes('optim') ? 'optimizing' : 'generating';

    const stepInsights = insightMap[stepKey] || [];
    if (stepInsights.length > 0) {
      const randomInsight = stepInsights[Math.floor(Math.random() * stepInsights.length)];
      setInsights(prev => {
        if (!prev.includes(randomInsight)) {
          return [...prev, randomInsight];
        }
        return prev;
      });
    }
  }, []);

  const updateCurrentStep = useCallback((stepName: string, progress: number) => {
    let stepIndex = 0;

    if (stepName.toLowerCase().includes('parsing')) {
      stepIndex = 0;
    } else if (stepName.toLowerCase().includes('analy')) {
      stepIndex = 1;
    } else if (stepName.toLowerCase().includes('optim')) {
      stepIndex = 2;
    } else if (stepName.toLowerCase().includes('latex') || stepName.toLowerCase().includes('generat')) {
      stepIndex = 3;
    } else if (stepName.toLowerCase().includes('complet') || stepName.toLowerCase().includes('final')) {
      stepIndex = 4;
    }

    setCurrentStepIndex(stepIndex);

    // Calculate step progress
    const stepStartProgress = (stepIndex / optimizationSteps.length) * 100;
    const stepEndProgress = ((stepIndex + 1) / optimizationSteps.length) * 100;
    const stepProgressPercent = Math.max(0, Math.min(100,
      ((progress - stepStartProgress) / (stepEndProgress - stepStartProgress)) * 100
    ));

    setStepProgress(stepProgressPercent);

    // Mark completed steps
    for (let i = 0; i < stepIndex; i++) {
      setCompletedSteps(prev => new Set([...prev, optimizationSteps[i].id]));
    }
  }, []);

  // Use useRef to store polling interval to avoid recreating it
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const stopPolling = useCallback(() => {
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
      pollIntervalRef.current = null;
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const startPolling = useCallback((jobIdToUse: string) => {
    // Stop any existing polling
    stopPolling();

    // Track retry times to implement exponential backoff
    let consecutiveErrors = 0;
    let retryDelay = 1000; // Start with 1 second
    let retryUntil: number | null = null;

    pollIntervalRef.current = setInterval(async () => {
      // If we're in a backoff period due to rate limiting or errors, wait
      if (retryUntil && Date.now() < retryUntil) {
        console.log(`Backing off until ${new Date(retryUntil).toLocaleTimeString()}`);
        return;
      }

      try {
        const status = await resumeAPI.getProcessingStatus(jobIdToUse);

        // Reset error counter on success
        consecutiveErrors = 0;
        retryDelay = 1000;

        // Update progress
        onProgressUpdate(status.progress);

        if (status.current_step !== currentStepName) {
          setCurrentStepName(status.current_step);
          addInsightForStep(status.current_step);
          updateCurrentStep(status.current_step, status.progress);
        }

        // If status contains a rate limit error, implement backoff
        if (status.error && status.error.toLowerCase().includes('rate limit')) {
          const match = status.error.match(/wait (\d+) seconds/i);
          if (match && match[1]) {
            const waitSeconds = parseInt(match[1]);
            retryUntil = Date.now() + (waitSeconds * 1000);
            setError(`Rate limit hit. Backing off for ${waitSeconds} seconds.`);
            console.log(`Rate limit detected. Backing off for ${waitSeconds} seconds.`);
            return;
          }
        }

        if (status.status === 'completed') {
          stopPolling();
          setIsProcessing(false);
          setError(null);

          // Get results
          try {
            const results = await resumeAPI.getProcessingResults(jobIdToUse);
            onComplete(results);
          } catch (err) {
            console.error('Error getting results:', err);
            onComplete();
          }
        } else if (status.status === 'failed') {
          stopPolling();
          setIsProcessing(false);
          setError(status.error || 'Processing failed');
        }

      } catch (err) {
        console.error('Error polling status:', err);

        // Implement exponential backoff
        consecutiveErrors++;
        retryDelay = Math.min(30000, retryDelay * 2); // Max 30 second delay
        retryUntil = Date.now() + retryDelay;

        console.log(`Error polling status. Backing off for ${retryDelay/1000} seconds.`);

        if (consecutiveErrors >= 10) {
          // Too many consecutive errors, stop polling
          stopPolling();
          setIsProcessing(false);
          setError('Failed to get processing status after multiple attempts');
        }
      }
    }, 2000); // Poll every 2 seconds instead of 1

    // Cleanup after 10 minutes
    timeoutRef.current = setTimeout(() => {
      stopPolling();
      setError('Processing timeout');
      setIsProcessing(false);
    }, 600000);
  }, [currentStepName, onProgressUpdate, onComplete, addInsightForStep, updateCurrentStep, stopPolling]);

  const startRealOptimization = useCallback(async () => {
    if (!uploadedFile) {
      setError("No file uploaded");
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Read file content
      const fileContent = await readFileContent(uploadedFile);

      // Start processing
      const response = await resumeAPI.processResume({
        resume_text: fileContent,
        target_role: extractTargetRole(jobDescription),
        target_industry: extractTargetIndustry(jobDescription)
      });

      setJobId(response.job_id);

      // Start polling for status updates
      startPolling(response.job_id);

    } catch (err) {
      console.error('Error starting optimization:', err);
      setError(`Failed to start optimization: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setIsProcessing(false);
    }
  }, [uploadedFile, jobDescription, startPolling]);

  useEffect(() => {
    if (!uploadedFile) return;

    startRealOptimization();
  }, [uploadedFile, startRealOptimization]);

  // Cleanup effect to stop polling when component unmounts
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result;
        if (typeof result === 'string') {
          resolve(result);
        } else {
          reject(new Error('Failed to read file content'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const extractTargetRole = (jobDesc: string): string => {
    // Simple extraction - you can make this more sophisticated
    const lines = jobDesc.split('\n');
    const firstLine = lines[0]?.toLowerCase() || '';

    if (firstLine.includes('software engineer') || firstLine.includes('developer')) {
      return 'Software Engineer';
    } else if (firstLine.includes('data scientist')) {
      return 'Data Scientist';
    } else if (firstLine.includes('product manager')) {
      return 'Product Manager';
    }

    return 'Software Engineer'; // Default
  };

  const extractTargetIndustry = (jobDesc: string): string => {
    const desc = jobDesc.toLowerCase();

    if (desc.includes('fintech') || desc.includes('finance')) {
      return 'Finance';
    } else if (desc.includes('healthcare') || desc.includes('medical')) {
      return 'Healthcare';
    } else if (desc.includes('startup')) {
      return 'Startup';
    }

    return 'Technology'; // Default
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.ceil(ms / 1000);
    return `~${seconds}s`;
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          AI Agents at Work
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Our specialized AI agents are analyzing and optimizing your resume for maximum impact.
        </p>
      </div>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <div>
                <h3 className="text-lg font-semibold text-red-900">Processing Error</h3>
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overall Progress */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Overall Progress</h3>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {Math.round(progress)}% Complete
            </Badge>
          </div>
          <Progress value={progress} className="h-3 mb-2" />
          <div className="flex justify-between text-sm text-gray-500">
            <span>
              {isProcessing ? 'Processing your resume...' : 'Waiting to start...'}
              {jobId && (
                <span className="ml-2 text-xs text-gray-400">Job ID: {jobId.slice(0, 8)}...</span>
              )}
            </span>
            <span>ETA: {Math.max(0, optimizationSteps.length - currentStepIndex - 1) * 30}s</span>
          </div>
        </CardContent>
      </Card>

      {/* Agent Steps */}
      <div className="space-y-4">
        {optimizationSteps.map((step, index) => {
          const isCompleted = completedSteps.has(step.id);
          const isCurrent = index === currentStepIndex;
          const isPending = index > currentStepIndex;

          return (
            <Card
              key={step.id}
              className={`transition-all duration-300 ${
                isCurrent
                  ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200'
                  : isCompleted
                  ? 'bg-green-50 border-green-200'
                  : 'opacity-60'
              }`}
            >
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-full ${
                    isCurrent
                      ? 'bg-blue-600 text-white'
                      : isCompleted
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-200 text-gray-500'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : isCurrent ? (
                      <div className="animate-spin">
                        <Bot className="h-6 w-6" />
                      </div>
                    ) : (
                      <step.icon className="h-6 w-6" />
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">{step.title}</h3>
                      <div className="flex items-center space-x-2">
                        {isCurrent && (
                          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatDuration(step.duration)}
                          </Badge>
                        )}
                        {isCompleted && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            Complete
                          </Badge>
                        )}
                        {isPending && (
                          <Badge variant="secondary" className="bg-gray-100 text-gray-600">
                            Pending
                          </Badge>
                        )}
                      </div>
                    </div>

                    <p className="text-gray-600 mb-3">{step.description}</p>

                    {isCurrent && (
                      <div className="space-y-2">
                        <Progress value={stepProgress} className="h-2" />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>Processing...</span>
                          <span>{Math.round(stepProgress)}%</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Real-time Insights */}
      {insights.length > 0 && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <h3 className="font-semibold text-green-900">Live Insights</h3>
            </div>
            <div className="space-y-2">
              {insights.slice(-3).map((insight, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <p className="text-sm text-green-800">{insight}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* File Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 mb-3">
              <FileText className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">Resume Analysis</h3>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">File:</span>
                <span className="font-medium">{uploadedFile?.name || "Unknown"}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Size:</span>
                <span className="font-medium">
                  {uploadedFile ? (uploadedFile.size / 1024 / 1024).toFixed(1) + " MB" : "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Type:</span>
                <span className="font-medium">{uploadedFile?.type || "Unknown"}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 mb-3">
              <Search className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">Job Analysis</h3>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Word Count:</span>
                <span className="font-medium">{jobDescription.trim().split(/\s+/).length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Characters:</span>
                <span className="font-medium">{jobDescription.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Match Score:</span>
                <span className="font-medium text-green-600">Calculating...</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
