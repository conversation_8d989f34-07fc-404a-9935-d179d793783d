"use client";

import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert } from "@/components/ui/alert";
import { Upload, FileText, CheckCircle, X, AlertCircle } from "lucide-react";

interface FileUploadSectionProps {
  uploadedFile: File | null;
  onFileUpload: (file: File) => void;
  onComplete: () => void;
}

export default function FileUploadSection({
  uploadedFile,
  onFileUpload,
  onComplete
}: FileUploadSectionProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    setIsUploading(true);
    setUploadError(null);

    // Simulate upload process
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      onFileUpload(file);
      setIsUploading(false);
    } catch {
      setIsUploading(false);
      setUploadError("Failed to upload file. Please try again.");
    }
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/msword': ['.doc'],
      'text/plain': ['.txt']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
    onDropRejected: (rejectedFiles) => {
      const rejection = rejectedFiles[0];
      if (rejection.errors.some(e => e.code === 'file-too-large')) {
        setUploadError("File is too large. Please upload a file smaller than 10MB.");
      } else if (rejection.errors.some(e => e.code === 'file-invalid-type')) {
        setUploadError("Invalid file type. Please upload a PDF, DOC, DOCX, or TXT file.");
      } else {
        setUploadError("Error uploading file. Please try again.");
      }
    }
  });

  const removeFile = () => {
    // Reset the uploaded file to null
    setUploadError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Upload Your Resume
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Start by uploading your current resume. We support PDF, Word documents, and text files.
        </p>
      </div>

      {/* Upload Area */}
      {!uploadedFile && (
        <Card className="border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors">
          <CardContent className="p-8">
            <div
              {...getRootProps()}
              className={`cursor-pointer text-center ${
                isDragActive ? "bg-blue-50" : ""
              } transition-colors rounded-lg p-8`}
            >
              <input {...getInputProps()} />

              <div className="space-y-4">
                <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  {isUploading ? (
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  ) : (
                    <Upload className="h-8 w-8 text-blue-600" />
                  )}
                </div>

                <div>
                  {isUploading ? (
                    <p className="text-lg font-semibold text-gray-900">Uploading...</p>
                  ) : (
                    <>
                      <p className="text-lg font-semibold text-gray-900">
                        {isDragActive
                          ? "Drop your resume here"
                          : "Drag & drop your resume, or click to browse"
                        }
                      </p>
                      <p className="text-sm text-gray-500 mt-2">
                        Supports PDF, DOC, DOCX, and TXT files (up to 10MB)
                      </p>
                    </>
                  )}
                </div>

                {!isUploading && (
                  <div className="flex justify-center space-x-4">
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      PDF
                    </Badge>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      DOC
                    </Badge>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      DOCX
                    </Badge>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      TXT
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Uploaded File Display */}
      {uploadedFile && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <FileText className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{uploadedFile.name}</h3>
                  <p className="text-sm text-gray-500">
                    {formatFileSize(uploadedFile.size)} • {uploadedFile.type || "Unknown type"}
                  </p>
                </div>
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={removeFile}
                className="text-gray-500 hover:text-red-600"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {uploadError && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <div className="text-red-800">
            <strong>Upload Error:</strong> {uploadError}
          </div>
        </Alert>
      )}

      {/* Continue Button */}
      {uploadedFile && (
        <div className="flex justify-center">
          <Button
            onClick={onComplete}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg"
            size="lg"
          >
            Continue to Job Description
            <FileText className="ml-2 h-5 w-5" />
          </Button>
        </div>
      )}

      {/* Help Section */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <h3 className="font-semibold text-blue-900 mb-3">💡 Tips for best results:</h3>
          <ul className="space-y-2 text-sm text-blue-800">
            <li>• Upload your most recent and complete resume</li>
            <li>• Ensure all text is selectable (not a scanned image)</li>
            <li>• Include all relevant experience and skills</li>
            <li>• PDF format typically provides the best text extraction</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
