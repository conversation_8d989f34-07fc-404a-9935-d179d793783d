"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert } from "@/components/ui/alert";
import {
  Download,
  Eye,
  FileText,
  TrendingUp,
  CheckCircle,
  Star,
  ArrowRight,
  RefreshCw,
  Share2
} from "lucide-react";

interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  linkedin?: string;
  github?: string;
  location?: string;
}

interface ResumeData {
  contact_info?: ContactInfo;
  professional_summary?: string;
  experience?: Array<{
    title?: string;
    company?: string;
    duration?: string;
    description?: string;
    achievements?: string[];
  }>;
  skills?: {
    technical?: string[];
    soft?: string[];
    tools?: string[];
  };
  education?: Array<{
    degree?: string;
    institution?: string;
    graduation_date?: string;
  }>;
}

interface AnalysisData {
  overall_score?: number;
  ats_compatibility_score?: number;
  job_match_score?: number;
  strengths?: string[];
  weaknesses?: string[];
  recommendations?: string[];
}

interface ResultsSectionProps {
  uploadedFile: File | null;
  jobDescription: string;
  processingResults?: {
    job_id: string;
    status: string;
    results: {
      parsed_resume: ResumeData;
      analysis: AnalysisData;
      optimized_resume: ResumeData;
      latex_code: string;
      metadata: Record<string, unknown>;
    };
    processing_time?: number;
    completed_at?: string;
  } | null;
}

const mockOptimizations = [
  {
    type: "Skills Enhancement",
    description: "Added 8 relevant technical skills mentioned in job description",
    impact: "High",
    details: [
      "Added React.js, TypeScript, Node.js to skills section",
      "Emphasized cloud platform experience (AWS)",
      "Highlighted database optimization skills"
    ]
  },
  {
    type: "Action Verbs Improvement",
    description: "Replaced weak verbs with powerful action words",
    impact: "Medium",
    details: [
      "Changed 'Worked on' to 'Architected and implemented'",
      "Replaced 'Helped with' to 'Led cross-functional team of'",
      "Updated 'Did' to 'Delivered'"
    ]
  },
  {
    type: "Quantified Achievements",
    description: "Added specific metrics and numbers where possible",
    impact: "High",
    details: [
      "Quantified team size: 'Led team of 8 developers'",
      "Added performance metrics: 'Improved load time by 40%'",
      "Included scale: 'Processed 1M+ daily transactions'"
    ]
  },
  {
    type: "ATS Optimization",
    description: "Improved keyword matching and formatting for ATS systems",
    impact: "Critical",
    details: [
      "Optimized section headers for ATS parsing",
      "Increased keyword density by 35%",
      "Ensured consistent formatting throughout"
    ]
  }
];

export default function ResultsSection({
  uploadedFile,
  jobDescription,
  processingResults
}: ResultsSectionProps) {
  const [activeTab, setActiveTab] = useState<'preview' | 'comparison' | 'changes'>('preview');
  const [isDownloading, setIsDownloading] = useState(false);

  // Calculate job description stats for display
  const jobDescriptionStats = {
    wordCount: jobDescription?.trim().split(/\s+/).length || 0,
    charCount: jobDescription?.length || 0
  };

  const fileName = uploadedFile?.name || "resume";

  // Extract actual data from processing results or use fallback
  const parsedResume = processingResults?.results?.parsed_resume;
  const optimizedResume = processingResults?.results?.optimized_resume;
  const analysisData = processingResults?.results?.analysis;
  const latexCode = processingResults?.results?.latex_code;

  const handleDownload = async (format: 'pdf' | 'latex') => {
    setIsDownloading(true);

    try {
      if (format === 'latex' && latexCode) {
        // Download LaTeX code directly
        const blob = new Blob([latexCode], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `optimized-${fileName.replace(/\.[^/.]+$/, "")}.tex`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else if (format === 'pdf') {
        // For PDF, we need to compile LaTeX on the backend
        alert('PDF compilation is not yet implemented. Please download the LaTeX file and compile it locally with XeLaTeX or pdflatex.');
      } else {
        alert('No LaTeX code available to download');
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    }

    setIsDownloading(false);
  };

  // Calculate metrics from actual data or use defaults
  const actualMetrics = {
    atsScore: analysisData?.ats_compatibility_score || 94,
    keywordMatch: analysisData?.job_match_score || 87,
    impactScore: analysisData?.overall_score || 91,
    readabilityScore: 89,
    overallScore: analysisData?.overall_score || 90
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600 bg-green-100";
    if (score >= 75) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getImpactColor = (impact: string) => {
    switch (impact.toLowerCase()) {
      case 'critical': return "bg-red-100 text-red-800";
      case 'high': return "bg-orange-100 text-orange-800";
      case 'medium': return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-2 mb-4">
          <CheckCircle className="h-8 w-8 text-green-600" />
          <h2 className="text-3xl font-bold text-gray-900">
            Optimization Complete!
          </h2>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Your resume has been successfully optimized with AI-powered improvements.
          Review the changes and download your professional PDF.
        </p>
      </div>

      {/* Success Alert */}
      <Alert className="border-green-200 bg-green-50">
        <TrendingUp className="h-4 w-4 text-green-600" />
        <div className="text-green-800">
          <strong>Great news!</strong> Your resume score improved from 67% to {actualMetrics.overallScore}% -
          a significant boost that will help you stand out to recruiters and ATS systems.
          Job description analysis: {jobDescriptionStats.wordCount} words, {jobDescriptionStats.charCount} characters.
        </div>
      </Alert>

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {[
          { label: "Overall Score", value: actualMetrics.overallScore, icon: Star },
          { label: "ATS Score", value: actualMetrics.atsScore, icon: CheckCircle },
          { label: "Keyword Match", value: actualMetrics.keywordMatch, icon: TrendingUp },
          { label: "Impact Score", value: actualMetrics.impactScore, icon: FileText },
          { label: "Readability", value: actualMetrics.readabilityScore, icon: Eye }
        ].map((metric) => (
          <Card key={metric.label}>
            <CardContent className="p-4 text-center">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-2 ${getScoreColor(metric.value)}`}>
                <metric.icon className="h-6 w-6" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{metric.value}%</div>
              <div className="text-sm text-gray-600">{metric.label}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs */}
      <Card>
        <CardHeader>
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {[
              { id: 'preview', label: 'Preview', icon: Eye },
              { id: 'comparison', label: 'Before/After', icon: RefreshCw },
              { id: 'changes', label: 'Changes Made', icon: TrendingUp }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as typeof activeTab)}
                className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {activeTab === 'preview' && (
            <div className="space-y-4">
              <div className="bg-white border-2 border-gray-200 rounded-lg p-8 min-h-[600px]">
                <div className="space-y-6">
                  <div className="text-center border-b pb-4">
                    <h1 className="text-2xl font-bold text-gray-900">
                      {optimizedResume?.contact_info?.name || parsedResume?.contact_info?.name || 'John Doe'}
                    </h1>
                    <p className="text-gray-600">
                      {optimizedResume?.professional_summary?.split('.')[0] || 'Senior Software Engineer'}
                    </p>
                    <p className="text-sm text-gray-500">
                      {optimizedResume?.contact_info?.email || parsedResume?.contact_info?.email || '<EMAIL>'} •
                      {optimizedResume?.contact_info?.phone || parsedResume?.contact_info?.phone || '(*************'} •
                      LinkedIn: {optimizedResume?.contact_info?.linkedin || parsedResume?.contact_info?.linkedin || '/in/johndoe'}
                    </p>
                  </div>

                  <div>
                    <h2 className="text-lg font-bold text-gray-900 mb-2">PROFESSIONAL SUMMARY</h2>
                    <p className="text-gray-700 text-sm leading-relaxed">
                      {optimizedResume?.professional_summary || parsedResume?.professional_summary ||
                       'Accomplished Senior Software Engineer with 5+ years of experience architecting and implementing scalable web applications using React, Node.js, and TypeScript. Led cross-functional teams of 8+ developers to deliver high-impact solutions processing 1M+ daily transactions. Proven expertise in cloud platforms (AWS) and database optimization, resulting in 40% performance improvements.'}
                    </p>
                  </div>

                  <div>
                    <h2 className="text-lg font-bold text-gray-900 mb-2">TECHNICAL SKILLS</h2>
                    <div className="text-sm text-gray-700">
                      {optimizedResume?.skills?.technical && optimizedResume.skills.technical.length > 0 ? (
                        <>
                          <strong>Technical Skills:</strong> {optimizedResume.skills.technical.join(', ')}<br/>
                        </>
                      ) : parsedResume?.skills?.technical && parsedResume.skills.technical.length > 0 ? (
                        <>
                          <strong>Technical Skills:</strong> {parsedResume.skills.technical.join(', ')}<br/>
                        </>
                      ) : (
                        <>
                          <strong>Programming Languages:</strong> JavaScript, TypeScript, Python, Java<br/>
                          <strong>Frontend:</strong> React.js, Next.js, Vue.js, HTML5, CSS3, Tailwind CSS<br/>
                          <strong>Backend:</strong> Node.js, Express.js, RESTful APIs, GraphQL<br/>
                          <strong>Cloud & DevOps:</strong> AWS (EC2, S3, Lambda), Docker, CI/CD, Kubernetes<br/>
                          <strong>Databases:</strong> PostgreSQL, MongoDB, Redis, Database Optimization<br/>
                        </>
                      )}
                      {optimizedResume?.skills?.tools && optimizedResume.skills.tools.length > 0 && (
                        <>
                          <strong>Tools & Technologies:</strong> {optimizedResume.skills.tools.join(', ')}<br/>
                        </>
                      )}
                      {optimizedResume?.skills?.soft && optimizedResume.skills.soft.length > 0 && (
                        <>
                          <strong>Soft Skills:</strong> {optimizedResume.skills.soft.join(', ')}<br/>
                        </>
                      )}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-lg font-bold text-gray-900 mb-2">PROFESSIONAL EXPERIENCE</h2>
                    <div className="space-y-4">
                      {optimizedResume?.experience && optimizedResume.experience.length > 0 ? (
                        optimizedResume.experience.slice(0, 3).map((exp, index) => (
                          <div key={index}>
                            <h3 className="font-semibold text-gray-900">{exp.title || 'Job Title'}</h3>
                            <p className="text-sm text-gray-600">{exp.company || 'Company'} • {exp.duration || 'Duration'}</p>
                            <div className="text-sm text-gray-700 mt-2">
                              {exp.description && <p className="mb-2">{exp.description}</p>}
                              {exp.achievements && exp.achievements.length > 0 && (
                                <ul className="space-y-1 list-disc list-inside">
                                  {exp.achievements.slice(0, 3).map((achievement, achIndex) => (
                                    <li key={achIndex}>{achievement}</li>
                                  ))}
                                </ul>
                              )}
                            </div>
                          </div>
                        ))
                      ) : parsedResume?.experience && parsedResume.experience.length > 0 ? (
                        parsedResume.experience.slice(0, 3).map((exp, index) => (
                          <div key={index}>
                            <h3 className="font-semibold text-gray-900">{exp.title || 'Job Title'}</h3>
                            <p className="text-sm text-gray-600">{exp.company || 'Company'} • {exp.duration || 'Duration'}</p>
                            <div className="text-sm text-gray-700 mt-2">
                              {exp.description && <p className="mb-2">{exp.description}</p>}
                              {exp.achievements && exp.achievements.length > 0 && (
                                <ul className="space-y-1 list-disc list-inside">
                                  {exp.achievements.slice(0, 3).map((achievement, achIndex) => (
                                    <li key={achIndex}>{achievement}</li>
                                  ))}
                                </ul>
                              )}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div>
                          <h3 className="font-semibold text-gray-900">Senior Software Engineer</h3>
                          <p className="text-sm text-gray-600">Tech Company Inc. • 2021 - Present</p>
                          <ul className="text-sm text-gray-700 mt-2 space-y-1 list-disc list-inside">
                            <li>Architected and implemented microservices architecture serving 1M+ daily active users</li>
                            <li>Led cross-functional team of 8 developers in agile development methodologies</li>
                            <li>Optimized database queries resulting in 40% improvement in application load times</li>
                            <li>Mentored junior developers and established code review best practices</li>
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'comparison' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                  <Badge variant="secondary" className="mr-2">Before</Badge>
                  Original Resume
                </h3>
                <div className="bg-gray-50 border rounded-lg p-4 h-96 overflow-y-auto">
                  <div className="text-sm text-gray-600 space-y-2">
                    <p><strong>Summary:</strong> Software engineer with experience in web development...</p>
                    <p><strong>Skills:</strong> JavaScript, React, Node.js</p>
                    <p><strong>Experience:</strong> Worked on various projects, helped with team tasks, did development work...</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                  <Badge className="mr-2 bg-green-100 text-green-800">After</Badge>
                  Optimized Resume
                </h3>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 h-96 overflow-y-auto">
                  <div className="text-sm text-gray-700 space-y-2">
                    <p><strong>Summary:</strong> Accomplished Senior Software Engineer with 5+ years of experience architecting and implementing scalable web applications...</p>
                    <p><strong>Skills:</strong> JavaScript, TypeScript, React.js, Node.js, AWS, Database Optimization</p>
                    <p><strong>Experience:</strong> Architected and implemented microservices, Led cross-functional team of 8 developers, Optimized performance by 40%...</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'changes' && (
            <div className="space-y-4">
              {mockOptimizations.map((optimization, index) => (
                <Card key={index} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-gray-900">{optimization.type}</h3>
                      <Badge className={getImpactColor(optimization.impact)}>
                        {optimization.impact} Impact
                      </Badge>
                    </div>
                    <p className="text-gray-600 mb-3">{optimization.description}</p>
                    <div className="space-y-1">
                      {optimization.details.map((detail, detailIndex) => (
                        <div key={detailIndex} className="flex items-start space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{detail}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Download Section */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Ready to download?</h3>
              <p className="text-gray-600">
                Your optimized resume is ready! Download as PDF for applications or LaTeX for further customization.
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={() => handleDownload('pdf')}
                disabled={isDownloading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isDownloading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Download PDF
              </Button>
              <Button
                variant="outline"
                onClick={() => handleDownload('latex')}
                disabled={isDownloading}
              >
                <FileText className="h-4 w-4 mr-2" />
                Download LaTeX
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ArrowRight className="h-5 w-5 text-blue-600" />
            <span>Next Steps</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Share2 className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Apply with Confidence</h3>
              <p className="text-sm text-gray-600">
                Your resume is now optimized for both ATS systems and human recruiters.
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Track Your Success</h3>
              <p className="text-sm text-gray-600">
                Monitor your application success rate and return for more optimizations.
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <RefreshCw className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Optimize for New Roles</h3>
              <p className="text-sm text-gray-600">
                Upload different job descriptions to create role-specific versions.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
