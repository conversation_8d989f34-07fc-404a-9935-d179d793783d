#!/usr/bin/env python3
"""
Quick resume processing test to debug parsing issues
"""

import requests
import json
import time
import sys

# Configuration
BACKEND_URL = "http://localhost:8003/api"

def quick_test():
    """Run a quick test with a short resume to debug the parsing issue"""
    print("🚀 Quick Resume Processing Test")
    print("=" * 40)

    # Test simple resume text
    simple_resume = """
<PERSON>
Software Engineer
<EMAIL> | (555) 123-4567

Experience:
- Software Engineer at TechCorp (2020-2023)
- Built web applications with React and Node.js
- Led team of 3 developers

Education:
- BS Computer Science, University (2020)

Skills:
- JavaScript, Python, React
"""

    # Prepare request
    request_data = {
        "resume_text": simple_resume.strip(),
        "target_role": "Software Engineer",
        "target_industry": "Technology"
    }

    print("📤 Sending simple resume processing request...")

    try:
        # Start processing
        response = requests.post(
            f"{BACKEND_URL}/ai/process-resume",
            json=request_data,
            timeout=10
        )

        if response.status_code != 200:
            print(f"❌ Processing request failed: {response.status_code}")
            print("Response:", response.text)
            return False

        result = response.json()
        job_id = result.get('job_id')
        print(f"✅ Processing started! Job ID: {job_id}")

        # Poll just a few times to see what happens
        for i in range(10):  # Only poll 10 times (50 seconds max)
            time.sleep(5)

            try:
                status_response = requests.get(
                    f"{BACKEND_URL}/ai/processing-status/{job_id}",
                    timeout=5
                )

                if status_response.status_code != 200:
                    print(f"❌ Status check failed: {status_response.status_code}")
                    continue

                status_data = status_response.json()
                status = status_data.get('status')
                progress = status_data.get('progress', 0)
                current_step = status_data.get('current_step', 'Unknown')
                error = status_data.get('error')

                print(f"📊 Attempt {i+1}: {progress}% - {current_step} ({status})")

                if error:
                    print(f"❗ Error details: {error}")

                if status == 'completed':
                    print("🎉 Processing completed!")
                    return True
                elif status == 'failed':
                    print(f"❌ Processing failed: {error}")
                    return False

            except Exception as e:
                print(f"❌ Status polling error: {e}")

        print("⏰ Test completed - check backend logs for actual processing status")
        return True

    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    sys.exit(0 if success else 1)
