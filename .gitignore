# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Next.js
.next/
out/
.vercel

# TypeScript
*.tsbuildinfo

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
log/

# Temporary files
tmp/
temp/
uploads/
*.tmp

# LaTeX
*.aux
*.bbl
*.blg
*.fdb_latexmk
*.fls
*.idx
*.ilg
*.ind
*.lof
*.log
*.lot
*.out
*.toc
*.synctex.gz
*.nav
*.snm
*.vrb

# PDF outputs (except templates)
*.pdf
!templates/*.pdf

# AI/ML
models/
*.pkl
*.joblib

# Testing
.coverage
.pytest_cache/
coverage/
htmlcov/

# Local development
local_*
dev_*
test_*
